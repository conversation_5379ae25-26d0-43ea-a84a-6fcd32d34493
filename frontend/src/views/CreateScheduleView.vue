<template>
  <div>
    <v-row>
      <v-col cols="12">
        <h1 class="text-h4 mb-4">T<PERSON><PERSON> l<PERSON>ch gi<PERSON>ng d<PERSON>y mới</h1>
      </v-col>
    </v-row>

    <v-card>
      <v-card-text>
        <v-form ref="form" @submit.prevent="createSchedule">
          <v-row>
            <!-- Academic Year and Semester Selection -->
            <v-col cols="12" md="6">
              <v-select
                v-model="selectedAcademicYear"
                :items="academicYears"
                item-title="year_code"
                item-value="id"
                label="Năm học *"
                :rules="[rules.required]"
                required
                @update:model-value="onAcademicYearChange"
              >
                <template v-slot:item="{ props, item }">
                  <v-list-item v-bind="props">
                    <v-list-item-title>{{ item.raw.year_code }}</v-list-item-title>
                    <v-list-item-subtitle>{{ item.raw.start_date }} - {{ item.raw.end_date }}</v-list-item-subtitle>
                  </v-list-item>
                </template>
              </v-select>
            </v-col>

            <v-col cols="12" md="6">
              <v-select
                v-model="schedule.semester"
                :items="filteredSemesters"
                item-title="name"
                item-value="id"
                label="Học kỳ *"
                :rules="[rules.required]"
                required
                :disabled="!selectedAcademicYear"
              >
                <template v-slot:item="{ props, item }">
                  <v-list-item v-bind="props">
                    <v-list-item-title>{{ item.raw.name }}</v-list-item-title>
                    <v-list-item-subtitle>{{ item.raw.start_date }} - {{ item.raw.end_date }}</v-list-item-subtitle>
                    <v-chip v-if="item.raw.is_current" color="success" size="x-small" class="ml-2">Hiện tại</v-chip>
                  </v-list-item>
                </template>
              </v-select>
            </v-col>

            <v-col cols="12" md="6">
              <v-select
                v-model="schedule.class_obj"
                :items="classes"
                item-title="name"
                item-value="id"
                label="Lớp học *"
                :rules="[rules.required]"
                required
              ></v-select>
            </v-col>

            <v-col cols="12" md="6">
              <v-select
                v-model="schedule.subject"
                :items="subjects"
                item-title="name"
                item-value="id"
                label="Môn học *"
                :rules="[rules.required]"
                required
                @update:model-value="loadLessons"
              ></v-select>
            </v-col>

            <v-col cols="12" md="6">
              <v-select
                v-model="schedule.lesson"
                :items="lessons"
                item-title="title"
                item-value="id"
                label="Bài học *"
                :rules="[rules.required]"
                required
                :disabled="!schedule.subject"
              ></v-select>
            </v-col>

            <!-- Schedule Details -->
            <v-col cols="12" md="6">
              <v-text-field
                v-model="schedule.schedule_date"
                type="date"
                label="Ngày học *"
                :rules="[rules.required]"
                required
              ></v-text-field>
            </v-col>

            <v-col cols="12" md="6">
              <v-select
                v-model="schedule.time_slot"
                :items="timeSlots"
                item-title="slot_name"
                item-value="id"
                label="Ca học *"
                :rules="[rules.required]"
                required
              ></v-select>
            </v-col>

            <v-col cols="12" md="6">
              <v-select
                v-model="schedule.lesson_type"
                :items="lessonTypes"
                item-title="text"
                item-value="value"
                label="Loại bài học *"
                :rules="[rules.required]"
                required
                @update:model-value="filterRooms"
              ></v-select>
            </v-col>

            <v-col cols="12" md="6">
              <v-select
                v-model="schedule.instructor"
                :items="instructors"
                item-title="full_name"
                item-value="id"
                label="Giảng viên *"
                :rules="[rules.required]"
                required
              ></v-select>
            </v-col>

            <v-col cols="12" md="6">
              <v-select
                v-model="schedule.room"
                :items="filteredRooms"
                item-title="name"
                item-value="id"
                label="Phòng học *"
                :rules="[rules.required]"
                required
              ></v-select>
            </v-col>

            <v-col cols="12" md="6" v-if="schedule.lesson_type === 'TH'">
              <v-select
                v-model="schedule.practice_group"
                :items="practiceGroups"
                item-title="group_number"
                item-value="id"
                label="Nhóm thực hành"
              ></v-select>
            </v-col>

            <!-- Additional Information -->
            <v-col cols="12" md="6">
              <v-text-field
                v-model="schedule.duration_hours"
                type="number"
                step="0.5"
                label="Số giờ học"
                :rules="[rules.required]"
                required
              ></v-text-field>
            </v-col>

            <v-col cols="12" md="6">
              <v-text-field
                v-model="schedule.coefficient"
                type="number"
                step="0.1"
                label="Hệ số"
                :rules="[rules.required]"
                required
              ></v-text-field>
            </v-col>

            <v-col cols="12">
              <v-textarea
                v-model="schedule.notes"
                label="Ghi chú"
                rows="3"
              ></v-textarea>
            </v-col>
          </v-row>

          <!-- Conflict Check Results -->
          <v-alert
            v-if="conflicts.length > 0"
            type="warning"
            class="mb-4"
          >
            <div class="text-h6 mb-2">Phát hiện xung đột:</div>
            <ul>
              <li v-for="conflict in conflicts" :key="conflict.type">
                {{ conflict.message }}
              </li>
            </ul>
          </v-alert>

          <!-- Actions -->
          <v-row class="mt-4">
            <v-col cols="12">
              <v-btn
                color="secondary"
                @click="checkConflicts"
                :loading="checkingConflicts"
                class="mr-2"
              >
                Kiểm tra xung đột
              </v-btn>
              <v-btn
                color="primary"
                type="submit"
                :loading="loading"
                :disabled="conflicts.length > 0"
              >
                Tạo lịch
              </v-btn>
              <v-btn
                text
                @click="$router.go(-1)"
                class="ml-2"
              >
                Hủy
              </v-btn>
            </v-col>
          </v-row>
        </v-form>
      </v-card-text>
    </v-card>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { useRouter } from 'vue-router'
import {
  scheduleAPI,
  semesterAPI,
  academicYearAPI,
  classAPI,
  subjectAPI,
  lessonAPI,
  timeSlotAPI,
  instructorAPI,
  roomAPI,
  practiceGroupAPI
} from '../services/api'
import { toast } from '@/composables/useToast'

const router = useRouter()

const loading = ref(false)
const checkingConflicts = ref(false)
const form = ref()

const schedule = ref({
  semester: null,
  class_obj: null,
  subject: null,
  lesson: null,
  schedule_date: '',
  time_slot: null,
  lesson_type: '',
  instructor: null,
  room: null,
  practice_group: null,
  duration_hours: 1.5,
  coefficient: 1.0,
  notes: ''
})

const selectedAcademicYear = ref(null)
const academicYears = ref([])
const semesters = ref([])
const classes = ref([])
const subjects = ref([])
const lessons = ref([])
const timeSlots = ref([])
const instructors = ref([])
const rooms = ref([])
const practiceGroups = ref([])
const conflicts = ref([])

const lessonTypes = [
  { text: 'Lý thuyết', value: 'LT' },
  { text: 'Thực hành', value: 'TH' }
]

const rules = {
  required: (value: any) => !!value || 'Trường này là bắt buộc'
}

const filteredRooms = computed(() => {
  if (!schedule.value.lesson_type) return rooms.value
  return rooms.value.filter(room => room.room_type === schedule.value.lesson_type)
})

const filteredSemesters = computed(() => {
  if (!selectedAcademicYear.value) return []
  return semesters.value.filter(semester => semester.academic_year === selectedAcademicYear.value)
})

onMounted(async () => {
  await loadInitialData()
  prefillFromQuery()
})

const prefillFromQuery = () => {
  const urlParams = new URLSearchParams(window.location.search)

  if (urlParams.get('date')) {
    schedule.value.schedule_date = urlParams.get('date')
  }

  if (urlParams.get('time_slot')) {
    schedule.value.time_slot = parseInt(urlParams.get('time_slot'))
  }

  if (urlParams.get('semester')) {
    schedule.value.semester = parseInt(urlParams.get('semester'))
  }
}

const loadInitialData = async () => {
  try {
    const [
      academicYearsRes,
      semestersRes,
      classesRes,
      subjectsRes,
      timeSlotsRes,
      instructorsRes,
      roomsRes
    ] = await Promise.all([
      academicYearAPI.list(),
      semesterAPI.list(),
      classAPI.list(),
      subjectAPI.list(),
      timeSlotAPI.list(),
      instructorAPI.list(),
      roomAPI.list()
    ])

    academicYears.value = academicYearsRes.data.results || academicYearsRes.data
    semesters.value = semestersRes.data.results || semestersRes.data
    classes.value = classesRes.data.results || classesRes.data
    subjects.value = subjectsRes.data.results || subjectsRes.data
    timeSlots.value = timeSlotsRes.data.results || timeSlotsRes.data
    instructors.value = instructorsRes.data.results || instructorsRes.data
    rooms.value = roomsRes.data.results || roomsRes.data

    // Set current academic year and semester as default
    await setCurrentDefaults()
  } catch (error) {
    console.error('Error loading initial data:', error)
    toast.error('Lỗi khi tải dữ liệu ban đầu')
  }
}

const setCurrentDefaults = async () => {
  try {
    // Try to get current semester from API
    const currentSemesterRes = await semesterAPI.current()
    const currentSemester = currentSemesterRes.data

    if (currentSemester) {
      selectedAcademicYear.value = currentSemester.academic_year
      schedule.value.semester = currentSemester.id
    } else {
      // Fallback: find current semester from list
      const currentSemesterFromList = semesters.value.find(s => s.is_current)
      if (currentSemesterFromList) {
        selectedAcademicYear.value = currentSemesterFromList.academic_year
        schedule.value.semester = currentSemesterFromList.id
      } else {
        // Fallback: use first available academic year and semester
        if (academicYears.value.length > 0) {
          selectedAcademicYear.value = academicYears.value[0].id
          const firstSemester = semesters.value.find(s => s.academic_year === selectedAcademicYear.value)
          if (firstSemester) {
            schedule.value.semester = firstSemester.id
          }
        }
      }
    }
  } catch (error) {
    console.error('Error setting current defaults:', error)
    // Fallback to first available options
    if (academicYears.value.length > 0) {
      selectedAcademicYear.value = academicYears.value[0].id
      const firstSemester = semesters.value.find(s => s.academic_year === selectedAcademicYear.value)
      if (firstSemester) {
        schedule.value.semester = firstSemester.id
      }
    }
  }
}

const loadLessons = async () => {
  if (!schedule.value.subject) return
  
  try {
    const response = await lessonAPI.list({ subject: schedule.value.subject })
    lessons.value = response.data.results || response.data
  } catch (error) {
    console.error('Error loading lessons:', error)
  }
}

const onAcademicYearChange = () => {
  // Reset semester when academic year changes
  schedule.value.semester = null

  // Auto-select current semester if available
  const currentSemester = filteredSemesters.value.find(s => s.is_current)
  if (currentSemester) {
    schedule.value.semester = currentSemester.id
  } else if (filteredSemesters.value.length > 0) {
    // Select first semester if no current semester
    schedule.value.semester = filteredSemesters.value[0].id
  }
}

const filterRooms = () => {
  // Rooms are filtered by computed property
}

const checkConflicts = async () => {
  checkingConflicts.value = true
  conflicts.value = []
  
  try {
    const response = await scheduleAPI.checkConflicts(schedule.value)
    if (response.data.has_conflicts) {
      conflicts.value = response.data.conflicts
    }
  } catch (error) {
    console.error('Error checking conflicts:', error)
  } finally {
    checkingConflicts.value = false
  }
}

const createSchedule = async () => {
  const { valid } = await form.value.validate()
  if (!valid) return

  loading.value = true
  try {
    await scheduleAPI.create(schedule.value)
    toast.success('Tạo lịch giảng dạy thành công!')
    router.push('/schedules')
  } catch (error: any) {
    console.error('Error creating schedule:', error)
    const errorMessage = error.response?.data?.detail ||
                        error.response?.data?.error ||
                        'Lỗi khi tạo lịch giảng dạy'
    toast.error(errorMessage)
  } finally {
    loading.value = false
  }
}
</script>
