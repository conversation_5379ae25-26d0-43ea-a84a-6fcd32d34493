<template>
  <v-container fluid>
    <!-- Header -->
    <v-row class="mb-4">
      <v-col>
        <div class="d-flex justify-space-between align-center">
          <h2>
            <v-icon class="mr-2">mdi-account-group</v-icon>
            <PERSON><PERSON>ản lý Người dùng
          </h2>
          <v-btn 
            color="primary"
            @click="showCreateDialog = true"
            prepend-icon="mdi-account-plus"
          >
            Thêm người dùng
          </v-btn>
        </div>
      </v-col>
    </v-row>

    <!-- Filters -->
    <v-card class="mb-4">
      <v-card-text>
        <v-row>
          <v-col cols="12" md="4">
            <v-text-field
              v-model="searchQuery"
              label="Tìm kiếm"
              placeholder="Tìm theo tên đăng nhập, họ tên, email..."
              prepend-inner-icon="mdi-magnify"
              clearable
              @input="debouncedSearch"
            ></v-text-field>
          </v-col>
          <v-col cols="12" md="3">
            <v-select
              v-model="roleFilter"
              label="Vai trò"
              :items="roleOptions"
              @update:model-value="loadUsers"
            ></v-select>
          </v-col>
          <v-col cols="12" md="3">
            <v-select
              v-model="statusFilter"
              label="Trạng thái"
              :items="statusOptions"
              @update:model-value="loadUsers"
            ></v-select>
          </v-col>
          <v-col cols="12" md="2" class="d-flex align-end">
            <v-btn 
              variant="outlined"
              @click="resetFilters"
              prepend-icon="mdi-refresh"
            >
              Đặt lại
            </v-btn>
          </v-col>
        </v-row>
      </v-card-text>
    </v-card>

    <!-- Loading -->
    <v-row v-if="loading" class="justify-center">
      <v-col cols="auto">
        <v-progress-circular
          indeterminate
          color="primary"
          size="64"
        ></v-progress-circular>
      </v-col>
    </v-row>

    <!-- User List -->
    <v-card v-else>
      <v-data-table
        :headers="headers"
        :items="users"
        :loading="loading"
        class="elevation-1"
      >
        <template v-slot:item.username="{ item }">
          <span class="font-weight-bold text-primary">{{ item.username }}</span>
        </template>
        
        <template v-slot:item.full_name="{ item }">
          {{ item.full_name || '-' }}
        </template>
        
        <template v-slot:item.email="{ item }">
          {{ item.email || '-' }}
        </template>
        
        <template v-slot:item.role_name="{ item }">
          <v-chip color="info" size="small">{{ item.role_name || 'Chưa phân quyền' }}</v-chip>
        </template>
        
        <template v-slot:item.date_joined="{ item }">
          {{ formatDate(item.date_joined) }}
        </template>
        
        <template v-slot:item.is_active="{ item }">
          <v-chip
            :color="item.is_active ? 'success' : 'grey'"
            size="small"
          >
            {{ item.is_active ? 'Hoạt động' : 'Không hoạt động' }}
          </v-chip>
        </template>
        
        <template v-slot:item.actions="{ item }">
          <v-btn
            icon="mdi-pencil"
            size="small"
            variant="text"
            color="primary"
            @click="editUser(item)"
            title="Chỉnh sửa"
          ></v-btn>
          <v-btn
            icon="mdi-key"
            size="small"
            variant="text"
            color="warning"
            @click="changePassword(item)"
            title="Đổi mật khẩu"
          ></v-btn>
          <v-btn
            icon="mdi-delete"
            size="small"
            variant="text"
            color="error"
            @click="deleteUser(item)"
            title="Xóa"
            :disabled="item.username === 'admin'"
          ></v-btn>
        </template>
        
        <template v-slot:no-data>
          <div class="text-center pa-4">
            <v-icon size="64" color="grey">mdi-account-group</v-icon>
            <div class="text-h6 mt-2">Không có người dùng nào</div>
          </div>
        </template>
      </v-data-table>
    </v-card>

    <!-- Create/Edit Dialog -->
    <v-dialog v-model="showCreateDialog" max-width="600px">
      <v-card>
        <v-card-title>
          <v-icon class="mr-2">mdi-account</v-icon>
          {{ editMode ? 'Chỉnh sửa người dùng' : 'Thêm người dùng mới' }}
        </v-card-title>
        
        <v-card-text>
          <v-form ref="form" v-model="valid">
            <v-text-field
              v-model="userForm.username"
              label="Tên đăng nhập"
              :rules="[rules.required]"
              :error-messages="errors.username"
              :disabled="editMode"
              :hint="editMode ? 'Tên đăng nhập không thể thay đổi' : ''"
              required
            ></v-text-field>
            
            <v-text-field
              v-model="userForm.full_name"
              label="Họ tên"
              :error-messages="errors.full_name"
            ></v-text-field>
            
            <v-text-field
              v-model="userForm.email"
              label="Email"
              type="email"
              :error-messages="errors.email"
            ></v-text-field>
            
            <v-select
              v-model="userForm.role"
              label="Vai trò"
              :items="roleSelectOptions"
              :error-messages="errors.role"
            ></v-select>
            
            <v-text-field
              v-if="!editMode"
              v-model="userForm.password"
              label="Mật khẩu"
              type="password"
              :rules="[rules.required]"
              :error-messages="errors.password"
              hint="Mật khẩu tối thiểu 8 ký tự"
              required
            ></v-text-field>
            
            <v-checkbox
              v-model="userForm.is_active"
              label="Hoạt động"
            ></v-checkbox>
            
            <v-checkbox
              v-model="userForm.is_staff"
              label="Nhân viên (có thể truy cập admin)"
            ></v-checkbox>
          </v-form>
        </v-card-text>
        
        <v-card-actions>
          <v-spacer></v-spacer>
          <v-btn @click="closeDialog">Hủy</v-btn>
          <v-btn 
            color="primary"
            @click="saveUser"
            :loading="saving"
          >
            {{ editMode ? 'Cập nhật' : 'Thêm mới' }}
          </v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>

    <!-- Change Password Dialog -->
    <v-dialog v-model="showPasswordDialog" max-width="400px">
      <v-card>
        <v-card-title>
          <v-icon class="mr-2">mdi-key</v-icon>
          Đổi mật khẩu
        </v-card-title>
        
        <v-card-text>
          <v-alert 
            v-if="userToChangePassword" 
            type="info" 
            variant="tonal"
            class="mb-3"
          >
            <strong>{{ userToChangePassword.username }}</strong> - {{ userToChangePassword.full_name }}
          </v-alert>
          
          <v-form ref="passwordForm" v-model="passwordValid">
            <v-text-field
              v-model="passwordForm.new_password"
              label="Mật khẩu mới"
              type="password"
              :rules="[rules.required, rules.minLength]"
              :error-messages="passwordErrors.new_password"
              hint="Mật khẩu tối thiểu 8 ký tự"
              required
            ></v-text-field>
            
            <v-text-field
              v-model="passwordForm.confirm_password"
              label="Xác nhận mật khẩu"
              type="password"
              :rules="[rules.required, rules.passwordMatch]"
              :error-messages="passwordErrors.confirm_password"
              required
            ></v-text-field>
          </v-form>
        </v-card-text>
        
        <v-card-actions>
          <v-spacer></v-spacer>
          <v-btn @click="showPasswordDialog = false">Hủy</v-btn>
          <v-btn 
            color="warning"
            @click="savePassword"
            :loading="changingPassword"
          >
            Đổi mật khẩu
          </v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>

    <!-- Delete Confirmation Dialog -->
    <v-dialog v-model="showDeleteDialog" max-width="400px">
      <v-card>
        <v-card-title class="text-error">
          <v-icon class="mr-2">mdi-alert</v-icon>
          Xác nhận xóa
        </v-card-title>
        
        <v-card-text>
          <p>Bạn có chắc chắn muốn xóa người dùng này?</p>
          <v-alert 
            v-if="userToDelete" 
            type="warning" 
            variant="tonal"
            class="mb-3"
          >
            <strong>{{ userToDelete.username }}</strong> - {{ userToDelete.full_name }}
            <br>
            <small>Email: {{ userToDelete.email }}</small>
            <br>
            <small>Vai trò: {{ userToDelete.role_name }}</small>
          </v-alert>
          <p class="text-caption">
            <v-icon size="small" class="mr-1">mdi-information</v-icon>
            Hành động này không thể hoàn tác.
          </p>
        </v-card-text>
        
        <v-card-actions>
          <v-spacer></v-spacer>
          <v-btn @click="showDeleteDialog = false">Hủy</v-btn>
          <v-btn 
            color="error"
            @click="confirmDelete"
            :loading="deleting"
          >
            Xóa
          </v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>
  </v-container>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { userAPI, roleAPI } from '@/services/api'
import { toast } from '@/composables/useToast'

// Reactive data
const users = ref([])
const roles = ref([])
const loading = ref(false)
const saving = ref(false)
const deleting = ref(false)
const changingPassword = ref(false)
const valid = ref(false)
const passwordValid = ref(false)
const editMode = ref(false)

// Dialogs
const showCreateDialog = ref(false)
const showDeleteDialog = ref(false)
const showPasswordDialog = ref(false)

// Forms
const userForm = ref({
  id: null,
  username: '',
  full_name: '',
  email: '',
  role: '',
  password: '',
  is_active: true,
  is_staff: false
})

const passwordForm = ref({
  new_password: '',
  confirm_password: ''
})

const userToDelete = ref(null)
const userToChangePassword = ref(null)
const errors = ref({})
const passwordErrors = ref({})

// Filters
const searchQuery = ref('')
const roleFilter = ref('')
const statusFilter = ref('')

// Table headers
const headers = [
  { title: 'Tên đăng nhập', key: 'username', sortable: true },
  { title: 'Họ tên', key: 'full_name', sortable: true },
  { title: 'Email', key: 'email', sortable: true },
  { title: 'Vai trò', key: 'role_name', sortable: false },
  { title: 'Ngày tạo', key: 'date_joined', sortable: true },
  { title: 'Trạng thái', key: 'is_active', sortable: false },
  { title: 'Thao tác', key: 'actions', sortable: false }
]

// Options
const statusOptions = [
  { title: 'Tất cả', value: '' },
  { title: 'Hoạt động', value: 'true' },
  { title: 'Không hoạt động', value: 'false' }
]

const roleOptions = computed(() => [
  { title: 'Tất cả vai trò', value: '' },
  ...roles.value.map(role => ({
    title: role.name,
    value: role.id
  }))
])

const roleSelectOptions = computed(() => 
  roles.value.map(role => ({
    title: role.name,
    value: role.id
  }))
)

// Validation rules
const rules = {
  required: (value: any) => !!value || 'Trường này là bắt buộc',
  minLength: (value: any) => !value || value.length >= 8 || 'Mật khẩu phải có ít nhất 8 ký tự',
  passwordMatch: (value: any) => value === passwordForm.value.new_password || 'Mật khẩu xác nhận không khớp'
}

// Methods
const loadRoles = async () => {
  try {
    const response = await roleAPI.list()
    roles.value = response.data.results || response.data
  } catch (error) {
    console.error('Error loading roles:', error)
  }
}

const loadUsers = async () => {
  try {
    loading.value = true
    const params = {}
    
    if (searchQuery.value) {
      params.search = searchQuery.value
    }
    
    if (roleFilter.value) {
      params.role = roleFilter.value
    }
    
    if (statusFilter.value !== '') {
      params.is_active = statusFilter.value
    }
    
    const response = await userAPI.list(params)
    users.value = response.data.results || response.data
  } catch (error) {
    console.error('Error loading users:', error)
    toast.error('Lỗi khi tải danh sách người dùng')
  } finally {
    loading.value = false
  }
}

// Debounced search
let searchTimeout: NodeJS.Timeout
const debouncedSearch = () => {
  clearTimeout(searchTimeout)
  searchTimeout = setTimeout(() => {
    loadUsers()
  }, 500)
}

const resetFilters = () => {
  searchQuery.value = ''
  roleFilter.value = ''
  statusFilter.value = ''
  loadUsers()
}

const resetForm = () => {
  userForm.value = {
    id: null,
    username: '',
    full_name: '',
    email: '',
    role: '',
    password: '',
    is_active: true,
    is_staff: false
  }
  errors.value = {}
  editMode.value = false
}

const resetPasswordForm = () => {
  passwordForm.value = {
    new_password: '',
    confirm_password: ''
  }
  passwordErrors.value = {}
}

const closeDialog = () => {
  showCreateDialog.value = false
  resetForm()
}

const editUser = (user: any) => {
  userForm.value = { 
    ...user,
    role: user.role || ''
  }
  editMode.value = true
  showCreateDialog.value = true
}

const saveUser = async () => {
  try {
    saving.value = true
    errors.value = {}

    if (editMode.value) {
      await userAPI.update(userForm.value.id, userForm.value)
      toast.success('Cập nhật người dùng thành công!')
    } else {
      await userAPI.create(userForm.value)
      toast.success('Thêm người dùng thành công!')
    }

    closeDialog()
    loadUsers()
  } catch (error: any) {
    console.error('Error saving user:', error)
    if (error.response?.data) {
      errors.value = error.response.data
    } else {
      toast.error('Lỗi khi lưu người dùng')
    }
  } finally {
    saving.value = false
  }
}

const changePassword = (user: any) => {
  userToChangePassword.value = user
  resetPasswordForm()
  showPasswordDialog.value = true
}

const savePassword = async () => {
  try {
    changingPassword.value = true
    passwordErrors.value = {}

    if (passwordForm.value.new_password !== passwordForm.value.confirm_password) {
      passwordErrors.value.confirm_password = 'Mật khẩu xác nhận không khớp'
      return
    }

    await userAPI.changePassword(userToChangePassword.value.id, {
      new_password: passwordForm.value.new_password
    })

    toast.success(`Đổi mật khẩu cho ${userToChangePassword.value.username} thành công!`)
    showPasswordDialog.value = false
    resetPasswordForm()
    userToChangePassword.value = null
  } catch (error: any) {
    console.error('Error changing password:', error)
    if (error.response?.data) {
      passwordErrors.value = error.response.data
    } else {
      toast.error('Lỗi khi đổi mật khẩu')
    }
  } finally {
    changingPassword.value = false
  }
}

const deleteUser = (user: any) => {
  userToDelete.value = user
  showDeleteDialog.value = true
}

const confirmDelete = async () => {
  try {
    deleting.value = true
    await userAPI.delete(userToDelete.value.id)
    toast.success(`Xóa người dùng ${userToDelete.value.username} thành công!`)
    showDeleteDialog.value = false
    userToDelete.value = null
    loadUsers()
  } catch (error) {
    console.error('Error deleting user:', error)
    toast.error('Lỗi khi xóa người dùng')
  } finally {
    deleting.value = false
  }
}

// Utility functions
const formatDate = (dateString: string) => {
  if (!dateString) return ''
  const date = new Date(dateString)
  return date.toLocaleDateString('vi-VN')
}

// Lifecycle
onMounted(() => {
  loadRoles()
  loadUsers()
})
</script>

<style scoped>
.text-primary {
  color: rgb(var(--v-theme-primary)) !important;
}
</style>
