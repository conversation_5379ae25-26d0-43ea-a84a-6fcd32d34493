<template>
  <div class="home-container">
    <!-- Animated Background -->
    <div class="background-animation">
      <div class="floating-shapes">
        <div class="shape shape-1"></div>
        <div class="shape shape-2"></div>
        <div class="shape shape-3"></div>
        <div class="shape shape-4"></div>
        <div class="shape shape-5"></div>
        <div class="shape shape-6"></div>
      </div>
    </div>

    <!-- Main Content -->
    <v-container class="fill-height" fluid>
      <v-row align="center" justify="center" class="fill-height">
        <v-col cols="12" sm="10" md="8" lg="6" xl="5">
          <!-- Hero Card -->
          <v-card class="hero-card" elevation="24">
            <!-- Header Section -->
            <div class="hero-header">
              <div class="logo-section">
                <v-avatar size="80" class="logo-avatar">
                  <v-icon size="40" color="white">mdi-calendar-account</v-icon>
                </v-avatar>
                <h1 class="hero-title">
                  <PERSON><PERSON> thống quản lý<br>
                  <span class="highlight">Lịch giảng dạy</span>
                </h1>
                <p class="hero-subtitle">
                  Giải pháp thông minh cho việc quản lý và sắp xếp lịch giảng dạy
                </p>
              </div>
            </div>

            <!-- Content Section -->
            <v-card-text class="hero-content">
              <div class="features-grid">
                <div class="feature-item">
                  <v-icon color="primary" size="32">mdi-calendar-check</v-icon>
                  <h3>Quản lý hiệu quả</h3>
                  <p>Sắp xếp lịch giảng dạy một cách khoa học và tối ưu</p>
                </div>
                <div class="feature-item">
                  <v-icon color="success" size="32">mdi-shield-check</v-icon>
                  <h3>Tránh xung đột</h3>
                  <p>Kiểm tra và ngăn ngừa xung đột lịch học tự động</p>
                </div>
                <div class="feature-item">
                  <v-icon color="warning" size="32">mdi-chart-line</v-icon>
                  <h3>Thống kê chi tiết</h3>
                  <p>Báo cáo và phân tích khối lượng giảng dạy</p>
                </div>
                <div class="feature-item">
                  <v-icon color="info" size="32">mdi-devices</v-icon>
                  <h3>Đa nền tảng</h3>
                  <p>Truy cập mọi lúc, mọi nơi trên mọi thiết bị</p>
                </div>
              </div>

              <!-- CTA Section -->
              <div class="cta-section">
                <v-btn
                  color="primary"
                  size="x-large"
                  to="/login"
                  prepend-icon="mdi-login"
                  class="cta-button"
                  elevation="8"
                >
                  Đăng nhập hệ thống
                </v-btn>
                <p class="cta-text">
                  Bắt đầu quản lý lịch giảng dạy của bạn ngay hôm nay
                </p>
              </div>
            </v-card-text>
          </v-card>

          <!-- Footer Info -->
          <div class="footer-info">
            <v-chip color="white" variant="tonal" class="ma-2">
              <v-icon start>mdi-school</v-icon>
              Dành cho giảng viên
            </v-chip>
            <v-chip color="white" variant="tonal" class="ma-2">
              <v-icon start>mdi-account-group</v-icon>
              Quản lý khoa
            </v-chip>
            <v-chip color="white" variant="tonal" class="ma-2">
              <v-icon start>mdi-cog</v-icon>
              Phòng đào tạo
            </v-chip>
          </div>
        </v-col>
      </v-row>
    </v-container>
  </div>
</template>

<script setup lang="ts">
import { onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '../stores/auth'
import { redirectToDashboard } from '../utils/roleRedirect'

const router = useRouter()
const authStore = useAuthStore()

onMounted(() => {
  // Redirect to appropriate dashboard if already authenticated
  if (authStore.isAuthenticated) {
    redirectToDashboard(router)
  }
})
</script>

<style scoped>
.home-container {
  position: relative;
  min-height: 100vh;
  width: 100vw;
  margin: 0;
  padding: 0;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  overflow: hidden;
}

/* Animated Background */
.background-animation {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  overflow: hidden;
  z-index: 0;
}

.floating-shapes {
  position: relative;
  width: 100%;
  height: 100%;
}

.shape {
  position: absolute;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
  animation: float 20s infinite linear;
}

.shape-1 {
  width: 80px;
  height: 80px;
  top: 10%;
  left: 10%;
  animation-delay: 0s;
}

.shape-2 {
  width: 120px;
  height: 120px;
  top: 20%;
  right: 15%;
  animation-delay: 5s;
}

.shape-3 {
  width: 60px;
  height: 60px;
  bottom: 30%;
  left: 20%;
  animation-delay: 10s;
}

.shape-4 {
  width: 100px;
  height: 100px;
  bottom: 20%;
  right: 10%;
  animation-delay: 15s;
}

.shape-5 {
  width: 40px;
  height: 40px;
  top: 50%;
  left: 5%;
  animation-delay: 8s;
}

.shape-6 {
  width: 90px;
  height: 90px;
  top: 70%;
  right: 25%;
  animation-delay: 12s;
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
    opacity: 0.7;
  }
  50% {
    transform: translateY(-20px) rotate(180deg);
    opacity: 1;
  }
}

/* Main Content */
.v-container {
  position: relative;
  z-index: 1;
  padding: 24px;
}

/* Hero Card */
.hero-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 24px;
  overflow: hidden;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
}

/* Header Section */
.hero-header {
  background: linear-gradient(135deg, #1976d2 0%, #1565c0 100%);
  padding: 48px 32px;
  text-align: center;
  color: white;
  position: relative;
}

.hero-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
  opacity: 0.3;
}

.logo-section {
  position: relative;
  z-index: 1;
}

.logo-avatar {
  background: rgba(255, 255, 255, 0.2);
  margin-bottom: 24px;
  border: 3px solid rgba(255, 255, 255, 0.3);
}

.hero-title {
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 16px;
  text-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
  line-height: 1.2;
}

.highlight {
  background: linear-gradient(45deg, #ffd700, #ffed4e);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.hero-subtitle {
  font-size: 1.2rem;
  opacity: 0.9;
  margin: 0;
  font-weight: 300;
}

/* Content Section */
.hero-content {
  padding: 48px 32px;
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 32px;
  margin-bottom: 48px;
}

.feature-item {
  text-align: center;
  padding: 24px;
  border-radius: 16px;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.feature-item:hover {
  transform: translateY(-8px);
  box-shadow: 0 12px 32px rgba(0, 0, 0, 0.15);
}

.feature-item h3 {
  font-size: 1.3rem;
  font-weight: 600;
  margin: 16px 0 8px 0;
  color: #2c3e50;
}

.feature-item p {
  font-size: 0.95rem;
  color: #6c757d;
  margin: 0;
  line-height: 1.5;
}

/* CTA Section */
.cta-section {
  text-align: center;
  padding: 32px 0;
}

.cta-button {
  font-size: 1.2rem;
  font-weight: 600;
  text-transform: none;
  padding: 16px 48px;
  border-radius: 50px;
  background: linear-gradient(135deg, #1976d2 0%, #1565c0 100%);
  transition: all 0.3s ease;
  margin-bottom: 16px;
}

.cta-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 12px 32px rgba(25, 118, 210, 0.4);
}

.cta-text {
  font-size: 1rem;
  color: #6c757d;
  margin: 16px 0 0 0;
  font-style: italic;
}

/* Footer Info */
.footer-info {
  text-align: center;
  margin-top: 32px;
  padding: 0 16px;
}

.footer-info .v-chip {
  margin: 4px 8px;
  font-weight: 500;
}

/* Responsive Design */
@media (max-width: 768px) {
  .hero-header {
    padding: 32px 24px;
  }

  .hero-title {
    font-size: 2rem;
  }

  .hero-subtitle {
    font-size: 1rem;
  }

  .hero-content {
    padding: 32px 24px;
  }

  .features-grid {
    grid-template-columns: 1fr;
    gap: 24px;
    margin-bottom: 32px;
  }

  .feature-item {
    padding: 20px;
  }

  .cta-button {
    font-size: 1.1rem;
    padding: 14px 32px;
  }
}

@media (max-width: 480px) {
  .v-container {
    padding: 16px;
  }

  .hero-title {
    font-size: 1.8rem;
  }

  .features-grid {
    gap: 16px;
  }

  .feature-item {
    padding: 16px;
  }

  .feature-item h3 {
    font-size: 1.1rem;
  }

  .feature-item p {
    font-size: 0.9rem;
  }
}
</style>
