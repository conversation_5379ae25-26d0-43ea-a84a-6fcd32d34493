<template>
  <div>
    <!-- No Instructor Profile Alert -->
    <v-alert
      v-if="!instructorInfo && !loadingInstructor && hasTriedLoadingInstructor"
      type="warning"
      variant="tonal"
      class="mb-6"
      prominent
    >
      <template v-slot:prepend>
        <v-icon size="32">mdi-account-alert</v-icon>
      </template>

      <v-alert-title class="text-h6 mb-2">
        Chưa có hồ sơ giảng viên
      </v-alert-title>

      <div class="mb-4">
        Bạn chưa có hồ sơ giảng viên trong hệ thống. <PERSON><PERSON> xem thông tin giảng dạy, bạn cần có hồ sơ giảng viên.
      </div>

      <div class="d-flex flex-wrap gap-3">
        <v-btn
          color="primary"
          variant="elevated"
          prepend-icon="mdi-account-plus"
          @click="showCreateProfileDialog = true"
        >
          Tạo hồ sơ giảng viên
        </v-btn>

        <v-btn
          color="info"
          variant="outlined"
          prepend-icon="mdi-refresh"
          @click="loadInstructorInfo"
          :loading="loadingInstructor"
        >
          Kiểm tra lại
        </v-btn>

        <v-btn
          color="warning"
          variant="text"
          prepend-icon="mdi-help-circle"
          @click="showHelpDialog = true"
        >
          Cần hỗ trợ?
        </v-btn>
      </div>
    </v-alert>

    <!-- Main Dashboard -->
    <div v-if="instructorInfo">
      <!-- Welcome Header -->
      <v-row class="mb-6">
        <v-col cols="12">
          <v-card class="welcome-card" color="primary" variant="flat">
            <v-card-text class="pa-6">
              <v-row align="center">
                <v-col cols="auto">
                  <v-avatar size="80" color="white">
                    <v-icon size="40" color="primary">mdi-account-tie</v-icon>
                  </v-avatar>
                </v-col>
                <v-col>
                  <h1 class="text-h4 text-white mb-2">
                    Chào mừng, {{ instructorInfo.full_name }}!
                  </h1>
                  <div class="text-h6 text-white opacity-90 mb-2">
                    Mã giảng viên: {{ instructorInfo.employee_code }}
                  </div>
                  <div class="text-body-1 text-white opacity-80">
                    {{ instructorInfo.department_name }} |
                    Giờ dạy tối đa: {{ instructorInfo.max_teaching_hours }} giờ/tuần
                  </div>
                </v-col>
                <v-col cols="auto">
                  <v-btn
                    color="white"
                    variant="outlined"
                    prepend-icon="mdi-calendar-account"
                    @click="showScheduleView = true"
                    size="large"
                  >
                    Xem lịch tuần
                  </v-btn>
                </v-col>
              </v-row>
            </v-card-text>
          </v-card>
        </v-col>
      </v-row>

      <!-- Statistics Cards -->
      <v-row class="mb-6">
        <v-col cols="12" md="3">
          <v-card class="stat-card" color="success" variant="tonal">
            <v-card-text class="text-center pa-6">
              <v-icon size="48" color="success" class="mb-3">mdi-clock-check</v-icon>
              <div class="text-h3 font-weight-bold text-success">{{ stats.totalHours }}</div>
              <div class="text-body-1 text-success">Tổng giờ giảng</div>
              <div class="text-caption text-success opacity-80">Học kỳ hiện tại</div>
            </v-card-text>
          </v-card>
        </v-col>

        <v-col cols="12" md="3">
          <v-card class="stat-card" color="info" variant="tonal">
            <v-card-text class="text-center pa-6">
              <v-icon size="48" color="info" class="mb-3">mdi-calendar-multiple</v-icon>
              <div class="text-h3 font-weight-bold text-info">{{ stats.totalSessions }}</div>
              <div class="text-body-1 text-info">Tổng buổi dạy</div>
              <div class="text-caption text-info opacity-80">Học kỳ hiện tại</div>
            </v-card-text>
          </v-card>
        </v-col>

        <v-col cols="12" md="3">
          <v-card class="stat-card" color="warning" variant="tonal">
            <v-card-text class="text-center pa-6">
              <v-icon size="48" color="warning" class="mb-3">mdi-google-classroom</v-icon>
              <div class="text-h3 font-weight-bold text-warning">{{ stats.totalClasses }}</div>
              <div class="text-body-1 text-warning">Lớp giảng dạy</div>
              <div class="text-caption text-warning opacity-80">Học kỳ hiện tại</div>
            </v-card-text>
          </v-card>
        </v-col>

        <v-col cols="12" md="3">
          <v-card class="stat-card" color="error" variant="tonal">
            <v-card-text class="text-center pa-6">
              <v-icon size="48" color="error" class="mb-3">mdi-book-open-variant</v-icon>
              <div class="text-h3 font-weight-bold text-error">{{ stats.totalSubjects }}</div>
              <div class="text-body-1 text-error">Môn học</div>
              <div class="text-caption text-error opacity-80">Học kỳ hiện tại</div>
            </v-card-text>
          </v-card>
        </v-col>
      </v-row>

      <!-- Teaching Hours Chart -->
      <v-row class="mb-6">
        <v-col cols="12" md="8">
          <v-card>
            <v-card-title class="d-flex align-center">
              <v-icon class="mr-2">mdi-chart-line</v-icon>
              Thống kê giờ giảng theo tuần
              <v-spacer></v-spacer>
              <v-select
                v-model="selectedSemester"
                :items="filteredSemesters"
                item-title="name"
                item-value="id"
                label="Học kỳ"
                variant="outlined"
                density="compact"
                style="max-width: 200px"
                @update:model-value="loadTeachingStats"
              ></v-select>
            </v-card-title>
            <v-card-text>
              <div class="teaching-hours-chart">
                <v-row>
                  <v-col cols="12">
                    <div class="text-h6 mb-4">Giờ giảng theo hệ số buổi</div>
                    <v-progress-linear
                      :model-value="(stats.totalHours / instructorInfo.max_teaching_hours) * 100"
                      height="20"
                      color="primary"
                      class="mb-2"
                    >
                      <template v-slot:default="{ value }">
                        <strong>{{ Math.ceil(value) }}%</strong>
                      </template>
                    </v-progress-linear>
                    <div class="d-flex justify-space-between text-caption">
                      <span>{{ stats.totalHours }} / {{ instructorInfo.max_teaching_hours }} giờ</span>
                      <span>Còn lại: {{ instructorInfo.max_teaching_hours - stats.totalHours }} giờ</span>
                    </div>
                  </v-col>
                </v-row>

                <v-divider class="my-4"></v-divider>

                <div class="text-h6 mb-4">Chi tiết theo loại buổi</div>
                <v-row>
                  <v-col cols="4">
                    <div class="text-center">
                      <v-icon size="32" color="orange" class="mb-2">mdi-weather-sunny</v-icon>
                      <div class="text-h6">{{ stats.morningHours }}</div>
                      <div class="text-caption">Buổi sáng</div>
                    </div>
                  </v-col>
                  <v-col cols="4">
                    <div class="text-center">
                      <v-icon size="32" color="blue" class="mb-2">mdi-weather-partly-cloudy</v-icon>
                      <div class="text-h6">{{ stats.afternoonHours }}</div>
                      <div class="text-caption">Buổi chiều</div>
                    </div>
                  </v-col>
                  <v-col cols="4">
                    <div class="text-center">
                      <v-icon size="32" color="indigo" class="mb-2">mdi-weather-night</v-icon>
                      <div class="text-h6">{{ stats.eveningHours }}</div>
                      <div class="text-caption">Buổi tối</div>
                    </div>
                  </v-col>
                </v-row>
              </div>
            </v-card-text>
          </v-card>
        </v-col>

        <v-col cols="12" md="4">
          <v-card class="mb-4">
            <v-card-title class="d-flex align-center">
              <v-icon class="mr-2">mdi-calendar-today</v-icon>
              Lịch tuần này
            </v-card-title>
            <v-card-text>
              <div class="text-center mb-4">
                <div class="text-h6">{{ weekTitle }}</div>
                <div class="text-caption text-grey">{{ getCurrentWeekRange() }}</div>
              </div>

              <v-list density="compact">
                <v-list-item
                  v-for="schedule in upcomingSchedules"
                  :key="schedule.id"
                  class="mb-2"
                >
                  <template v-slot:prepend>
                    <v-avatar size="32" :color="getSessionColor(schedule.time_slot_session)">
                      <v-icon size="16" color="white">mdi-clock</v-icon>
                    </v-avatar>
                  </template>

                  <v-list-item-title class="text-body-2">
                    {{ schedule.class_code }} - {{ schedule.subject_code }}
                  </v-list-item-title>
                  <v-list-item-subtitle class="text-caption">
                    {{ formatScheduleTime(schedule) }}
                  </v-list-item-subtitle>

                  <template v-slot:append>
                    <v-chip size="x-small" :color="schedule.lesson_type === 'LT' ? 'blue' : 'green'">
                      {{ schedule.lesson_type }}
                    </v-chip>
                  </template>
                </v-list-item>
              </v-list>

              <v-btn
                block
                variant="outlined"
                prepend-icon="mdi-calendar-month"
                @click="showScheduleView = true"
                class="mt-4"
              >
                Xem lịch đầy đủ
              </v-btn>
            </v-card-text>
          </v-card>

          <v-card>
            <v-card-title class="d-flex align-center">
              <v-icon class="mr-2">mdi-download</v-icon>
              Xuất báo cáo
            </v-card-title>
            <v-card-text>
              <v-btn
                block
                color="success"
                prepend-icon="mdi-file-excel"
                @click="exportToExcel"
                :loading="exporting"
                class="mb-2"
              >
                Xuất Excel
              </v-btn>
              <v-btn
                block
                color="error"
                prepend-icon="mdi-file-pdf-box"
                @click="exportToPdf"
                :loading="exportingPdf"
              >
                Xuất PDF
              </v-btn>
            </v-card-text>
          </v-card>
        </v-col>
      </v-row>

      <!-- Subject Statistics -->
      <v-row class="mb-6">
        <v-col cols="12">
          <v-card>
            <v-card-title class="d-flex align-center">
              <v-icon class="mr-2">mdi-book-open-variant</v-icon>
              Thống kê môn học giảng dạy
            </v-card-title>
            <v-card-text>
              <v-data-table
                :headers="subjectHeaders"
                :items="subjectStats"
                :loading="loadingStats"
                class="elevation-0"
                density="compact"
              >
                <template v-slot:item.subject_code="{ item }">
                  <div class="font-weight-medium">{{ item.subject_code }}</div>
                </template>

                <template v-slot:item.total_hours="{ item }">
                  <v-chip color="success" size="small">
                    {{ item.total_hours }} giờ
                  </v-chip>
                </template>

                <template v-slot:item.total_sessions="{ item }">
                  <v-chip color="info" size="small">
                    {{ item.total_sessions }} buổi
                  </v-chip>
                </template>

                <template v-slot:item.lesson_types="{ item }">
                  <div class="d-flex gap-1">
                    <v-chip v-if="item.theory_hours > 0" color="blue" size="x-small">
                      LT: {{ item.theory_hours }}h
                    </v-chip>
                    <v-chip v-if="item.practice_hours > 0" color="green" size="x-small">
                      TH: {{ item.practice_hours }}h
                    </v-chip>
                  </div>
                </template>
              </v-data-table>
            </v-card-text>
          </v-card>
        </v-col>
      </v-row>
    </div>

    <!-- Full Calendar View Dialog -->
    <v-dialog v-model="showScheduleView" fullscreen hide-overlay transition="dialog-bottom-transition">
      <v-card>
        <v-toolbar color="primary" dark>
          <v-btn icon @click="showScheduleView = false">
            <v-icon>mdi-close</v-icon>
          </v-btn>
          <v-toolbar-title>
            <v-icon class="mr-2">mdi-calendar-month</v-icon>
            Lịch giảng dạy đầy đủ - {{ instructorInfo?.full_name }}
          </v-toolbar-title>
          <v-spacer></v-spacer>
          <v-btn icon @click="previousWeek" :loading="loading">
            <v-icon>mdi-chevron-left</v-icon>
          </v-btn>
          <v-toolbar-title class="text-h6 mx-4">{{ weekTitle }}</v-toolbar-title>
          <v-btn icon @click="nextWeek" :loading="loading">
            <v-icon>mdi-chevron-right</v-icon>
          </v-btn>
          <v-btn color="white" variant="outlined" @click="goToToday" :loading="loading" class="ml-4">
            Hôm nay
          </v-btn>
        </v-toolbar>

        <v-card-text class="pa-0 fill-height">
          <v-overlay :model-value="loading" class="align-center justify-center">
            <v-progress-circular color="primary" indeterminate size="64"></v-progress-circular>
          </v-overlay>

          <div class="calendar-container-fullscreen">
            <div class="calendar-grid-fullscreen">
              <!-- Time slots header -->
              <div class="time-header-fullscreen">
                <div class="header-content">
                  <div class="time-label">Thời gian</div>
                </div>
              </div>
              <div v-for="day in weekDays" :key="day.date" class="day-header-fullscreen" :class="{ 'today': isToday(day.date) }">
                <div class="day-name">{{ day.name }}</div>
                <div class="day-date">{{ formatDisplayDate(day.date) }}</div>
                <div class="day-count">{{ getScheduleCountForDay(day.date) }} lịch</div>
              </div>

              <!-- Calendar body -->
              <template v-for="timeSlot in timeSlots" :key="timeSlot.id">
                <div class="time-slot-label-fullscreen">
                  <div class="slot-name">{{ timeSlot.slot_name }}</div>
                  <div class="slot-time">{{ timeSlot.start_time }} - {{ timeSlot.end_time }}</div>
                  <div class="slot-session">{{ getSessionText(timeSlot.session) }}</div>
                </div>

                <div v-for="day in weekDays" :key="`${timeSlot.id}-${day.date}`"
                     class="calendar-cell-fullscreen"
                     :class="{ 'today': isToday(day.date) }">
                  <div
                    v-for="schedule in getSchedulesForSlot(day.date, timeSlot.id)"
                    :key="schedule.id"
                    class="schedule-item-fullscreen"
                    :class="getScheduleClass(schedule)"
                    @click="showScheduleDetails(schedule)"
                  >
                    <div class="schedule-header">
                      <v-chip size="x-small" :color="schedule.lesson_type === 'LT' ? 'blue' : 'green'">
                        {{ schedule.lesson_type }}
                      </v-chip>
                      <v-chip size="x-small" :color="getStatusColor(schedule.status)">
                        {{ getStatusText(schedule.status) }}
                      </v-chip>
                    </div>
                    <div class="schedule-class">{{ schedule.class_code }}</div>
                    <div class="schedule-subject">{{ schedule.subject_code }}</div>
                    <div class="schedule-room">
                      <v-icon size="12">mdi-door</v-icon>
                      {{ schedule.room_code }}
                    </div>
                    <div class="schedule-instructor">
                      <v-icon size="12">mdi-account</v-icon>
                      {{ schedule.instructor_name }}
                    </div>
                  </div>

                  <!-- Empty slot indicator -->
                  <div v-if="getSchedulesForSlot(day.date, timeSlot.id).length === 0" class="empty-slot-fullscreen">
                    <v-icon color="grey-lighten-2">mdi-calendar-blank</v-icon>
                    <span class="empty-text">Trống</span>
                  </div>
                </div>
              </template>
            </div>
          </div>
        </v-card-text>
      </v-card>
    </v-dialog>

    <!-- Create Profile Dialog -->
    <CreateInstructorProfileDialog
      v-model="showCreateProfileDialog"
      @success="handleProfileCreated"
    />

    <!-- Help Dialog -->
    <HelpDialog
      v-model="showHelpDialog"
      @create-profile="showCreateProfileDialog = true"
    />

    <!-- Schedule Details Dialog -->
    <v-dialog v-model="detailsDialog" max-width="700px">
      <v-card v-if="selectedSchedule">
        <v-card-title class="d-flex align-center">
          <v-icon class="mr-2">mdi-calendar-clock</v-icon>
          Chi tiết lịch giảng dạy
          <v-spacer></v-spacer>
          <v-chip :color="getStatusColor(selectedSchedule.status)" size="small">
            {{ getStatusText(selectedSchedule.status) }}
          </v-chip>
        </v-card-title>
        <v-divider></v-divider>
        <v-card-text>
          <v-row>
            <v-col cols="12" md="6">
              <v-list-item>
                <v-list-item-title>
                  <v-icon class="mr-2">mdi-calendar</v-icon>
                  Ngày học
                </v-list-item-title>
                <v-list-item-subtitle>{{ formatDate(selectedSchedule.schedule_date) }}</v-list-item-subtitle>
              </v-list-item>
            </v-col>
            <v-col cols="12" md="6">
              <v-list-item>
                <v-list-item-title>
                  <v-icon class="mr-2">mdi-clock</v-icon>
                  Ca học
                </v-list-item-title>
                <v-list-item-subtitle>{{ selectedSchedule.time_slot_name }} ({{ selectedSchedule.time_slot_start }} - {{ selectedSchedule.time_slot_end }})</v-list-item-subtitle>
              </v-list-item>
            </v-col>
            <v-col cols="12" md="6">
              <v-list-item>
                <v-list-item-title>
                  <v-icon class="mr-2">mdi-google-classroom</v-icon>
                  Lớp học
                </v-list-item-title>
                <v-list-item-subtitle>{{ selectedSchedule.class_name }} ({{ selectedSchedule.class_code }})</v-list-item-subtitle>
              </v-list-item>
            </v-col>
            <v-col cols="12" md="6">
              <v-list-item>
                <v-list-item-title>
                  <v-icon class="mr-2">mdi-book-open-variant</v-icon>
                  Môn học
                </v-list-item-title>
                <v-list-item-subtitle>{{ selectedSchedule.subject_name }} ({{ selectedSchedule.subject_code }})</v-list-item-subtitle>
              </v-list-item>
            </v-col>
            <v-col cols="12" md="6">
              <v-list-item>
                <v-list-item-title>
                  <v-icon class="mr-2">mdi-book</v-icon>
                  Bài học
                </v-list-item-title>
                <v-list-item-subtitle>{{ selectedSchedule.lesson_title }}</v-list-item-subtitle>
              </v-list-item>
            </v-col>
            <v-col cols="12" md="6">
              <v-list-item>
                <v-list-item-title>
                  <v-icon class="mr-2">mdi-door</v-icon>
                  Phòng học
                </v-list-item-title>
                <v-list-item-subtitle>{{ selectedSchedule.room_name }} ({{ selectedSchedule.room_code }})</v-list-item-subtitle>
              </v-list-item>
            </v-col>
            <v-col cols="12" md="6">
              <v-list-item>
                <v-list-item-title>
                  <v-icon class="mr-2">mdi-format-list-bulleted-type</v-icon>
                  Loại bài học
                </v-list-item-title>
                <v-list-item-subtitle>
                  <v-chip :color="selectedSchedule.lesson_type === 'LT' ? 'blue' : 'green'" size="small">
                    {{ selectedSchedule.lesson_type === 'LT' ? 'Lý thuyết' : 'Thực hành' }}
                  </v-chip>
                </v-list-item-subtitle>
              </v-list-item>
            </v-col>
            <v-col cols="12" md="6">
              <v-list-item>
                <v-list-item-title>
                  <v-icon class="mr-2">mdi-timer</v-icon>
                  Thời lượng
                </v-list-item-title>
                <v-list-item-subtitle>{{ selectedSchedule.duration_hours }} giờ</v-list-item-subtitle>
              </v-list-item>
            </v-col>
            <v-col cols="12" v-if="selectedSchedule.practice_group_number">
              <v-list-item>
                <v-list-item-title>
                  <v-icon class="mr-2">mdi-account-group</v-icon>
                  Nhóm thực hành
                </v-list-item-title>
                <v-list-item-subtitle>Nhóm {{ selectedSchedule.practice_group_number }}</v-list-item-subtitle>
              </v-list-item>
            </v-col>
            <v-col cols="12" v-if="selectedSchedule.notes">
              <v-list-item>
                <v-list-item-title>
                  <v-icon class="mr-2">mdi-note-text</v-icon>
                  Ghi chú
                </v-list-item-title>
                <v-list-item-subtitle>{{ selectedSchedule.notes }}</v-list-item-subtitle>
              </v-list-item>
            </v-col>
          </v-row>
        </v-card-text>
        <v-divider></v-divider>
        <v-card-actions>
          <v-spacer></v-spacer>
          <v-btn text @click="detailsDialog = false">Đóng</v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import {
  scheduleAPI,
  timeSlotAPI,
  academicYearAPI,
  semesterAPI,
  instructorAPI,
  classAPI,
  roomAPI
} from '../services/api'
import { useAuthStore } from '@/stores/auth'
import { toast } from '@/composables/useToast'
import CreateInstructorProfileDialog from '@/components/CreateInstructorProfileDialog.vue'
import HelpDialog from '@/components/HelpDialog.vue'

const authStore = useAuthStore()

const loading = ref(false)
const loadingInstructor = ref(false)
const loadingStats = ref(false)
const hasTriedLoadingInstructor = ref(false)
const exporting = ref(false)
const exportingPdf = ref(false)

// Use string date for better reactivity
const getCurrentMondayString = () => {
  const today = new Date()
  const monday = new Date(today)
  monday.setDate(monday.getDate() - monday.getDay() + 1)
  return monday.toISOString().split('T')[0]
}

const currentWeekStart = ref(getCurrentMondayString())
const schedules = ref([])
const weekSchedules = ref([])
const upcomingSchedules = ref([])
const timeSlots = ref([])
const academicYears = ref([])
const semesters = ref([])
const allSemesters = ref([])
const classes = ref([])
const rooms = ref([])
const instructorInfo = ref(null)
const detailsDialog = ref(false)
const selectedSchedule = ref(null)
const showCreateProfileDialog = ref(false)
const showHelpDialog = ref(false)
const showScheduleView = ref(false)
const selectedSemester = ref(null)

// Statistics data
const stats = ref({
  totalHours: 0,
  totalSessions: 0,
  totalClasses: 0,
  totalSubjects: 0,
  morningHours: 0,
  afternoonHours: 0,
  eveningHours: 0
})

const subjectStats = ref([])
const subjectHeaders = [
  { title: 'Mã môn', key: 'subject_code', sortable: true },
  { title: 'Tên môn học', key: 'subject_name', sortable: true },
  { title: 'Lớp học', key: 'classes', sortable: false },
  { title: 'Tổng giờ', key: 'total_hours', sortable: true },
  { title: 'Số buổi', key: 'total_sessions', sortable: true },
  { title: 'Loại bài', key: 'lesson_types', sortable: false }
]

const filters = ref({
  academicYear: null,
  semester: null,
  instructor: null, // This will be locked to current instructor
  class: null,
  room: null
})

// Computed property for filtered semesters based on academic year
const filteredSemesters = computed(() => {
  if (!filters.value.academicYear) {
    return allSemesters.value
  }
  return allSemesters.value.filter((s: any) => s.academic_year === filters.value.academicYear)
})

const weekDays = computed(() => {
  const days = []
  const dayNames = ['Thứ 2', 'Thứ 3', 'Thứ 4', 'Thứ 5', 'Thứ 6', 'Thứ 7', 'Chủ nhật']

  for (let i = 0; i < 7; i++) {
    const date = new Date(currentWeekStart.value + 'T00:00:00')
    date.setDate(date.getDate() + i)
    days.push({
      name: dayNames[i],
      date: date.toISOString().split('T')[0]
    })
  }
  return days
})

const weekTitle = computed(() => {
  const start = new Date(currentWeekStart.value + 'T00:00:00')
  const end = new Date(currentWeekStart.value + 'T00:00:00')
  end.setDate(end.getDate() + 6)

  return `${start.toLocaleDateString('vi-VN')} - ${end.toLocaleDateString('vi-VN')}`
})

// Load instructor info first
const loadInstructorInfo = async () => {
  try {
    loadingInstructor.value = true
    hasTriedLoadingInstructor.value = true
    const response = await instructorAPI.myProfile()
    instructorInfo.value = response.data

    // Lock instructor filter to current instructor
    filters.value.instructor = instructorInfo.value.id

    // Load initial data after getting instructor info
    await loadInitialData()
    await loadTeachingStats()
    await loadUpcomingSchedules()
  } catch (error: any) {
    console.error('Error loading instructor info:', error)
    if (error.response?.status === 404) {
      instructorInfo.value = null
    } else {
      toast.error('Lỗi khi tải thông tin giảng viên')
    }
  } finally {
    loadingInstructor.value = false
  }
}

onMounted(async () => {
  await loadInstructorInfo()
})

const loadInitialData = async () => {
  try {
    const [timeSlotsRes, academicYearsRes, semestersRes, classesRes, roomsRes] = await Promise.all([
      timeSlotAPI.list(),
      academicYearAPI.list(),
      semesterAPI.list(),
      classAPI.list(),
      roomAPI.list()
    ])

    timeSlots.value = timeSlotsRes.data.results || timeSlotsRes.data
    academicYears.value = academicYearsRes.data.results || academicYearsRes.data
    allSemesters.value = semestersRes.data.results || semestersRes.data
    classes.value = classesRes.data.results || classesRes.data
    rooms.value = roomsRes.data.results || roomsRes.data

    // Set current academic year and semester as default
    const currentAcademicYear = academicYears.value.find((ay: any) => ay.is_current)
    if (currentAcademicYear) {
      filters.value.academicYear = currentAcademicYear.id
      selectedSemester.value = null // Reset selected semester when academic year changes
      console.log('Set current academic year:', currentAcademicYear.year_code)
    }

    const currentSemester = allSemesters.value.find((s: any) => s.is_current)
    if (currentSemester) {
      filters.value.semester = currentSemester.id
      selectedSemester.value = currentSemester.id
      console.log('Set current semester:', currentSemester.name)
    }

    console.log('Initial data loaded:', {
      timeSlots: timeSlots.value.length,
      academicYears: academicYears.value.length,
      semesters: allSemesters.value.length,
      classes: classes.value.length,
      rooms: rooms.value.length,
      currentFilters: filters.value
    })
  } catch (error) {
    console.error('Error loading initial data:', error)
    toast.error('Lỗi khi tải dữ liệu ban đầu')
  }
}

const onAcademicYearChange = () => {
  filters.value.semester = null
  selectedSemester.value = null
  loadTeachingStats()
}

// Load teaching statistics
const loadTeachingStats = async () => {
  if (!instructorInfo.value) return

  loadingStats.value = true
  try {
    const params: any = {}

    if (selectedSemester.value) {
      params.semester = selectedSemester.value
    } else if (filters.value.semester) {
      params.semester = filters.value.semester
    }

    if (filters.value.academicYear) {
      params.academic_year = filters.value.academicYear
    }

    // Use new statistics API
    const response = await instructorAPI.myStatistics(params)
    const data = response.data

    // Update statistics
    stats.value = data.statistics
    subjectStats.value = data.subject_statistics

    console.log('Teaching statistics loaded:', data)

  } catch (error) {
    console.error('Error loading teaching stats:', error)
    toast.error('Lỗi khi tải thống kê giảng dạy')
  } finally {
    loadingStats.value = false
  }
}



// Load upcoming schedules for this week
const loadUpcomingSchedules = async () => {
  if (!instructorInfo.value) return

  try {
    const response = await scheduleAPI.weekly(currentWeekStart.value)
    let scheduleData = response.data.schedules || response.data || []

    console.log('Raw weekly schedule data:', scheduleData.length, 'items')

    // Filter by instructor
    scheduleData = scheduleData.filter(s => s.instructor === instructorInfo.value.id)
    console.log('After instructor filter:', scheduleData.length, 'items')

    // Apply semester filter if selected
    if (selectedSemester.value || filters.value.semester) {
      const semesterFilter = selectedSemester.value || filters.value.semester
      scheduleData = scheduleData.filter(s => s.semester === semesterFilter)
      console.log('After semester filter:', scheduleData.length, 'items')
    }

    // Sort by date and time
    scheduleData.sort((a, b) => {
      const dateCompare = new Date(a.schedule_date).getTime() - new Date(b.schedule_date).getTime()
      if (dateCompare !== 0) return dateCompare
      return a.time_slot_start?.localeCompare(b.time_slot_start) || 0
    })

    weekSchedules.value = scheduleData
    schedules.value = scheduleData // For calendar grid
    upcomingSchedules.value = scheduleData.slice(0, 5) // Show only next 5 schedules

    console.log('Final weekly schedules:', weekSchedules.value.length)

  } catch (error) {
    console.error('Error loading upcoming schedules:', error)
  }
}

const loadWeeklySchedules = async () => {
  if (!instructorInfo.value) {
    console.log('No instructor info, skipping schedule load')
    return
  }

  loading.value = true
  try {
    const dateString = currentWeekStart.value
    console.log('Loading weekly schedules for:', {
      date: dateString,
      instructor: instructorInfo.value.id,
      instructorName: instructorInfo.value.full_name,
      filters: filters.value
    })

    // Call API without additional params first to get all data
    const response = await scheduleAPI.weekly(dateString)
    console.log('API Response:', response.data)

    let scheduleData = response.data.schedules || response.data || []
    console.log('Raw schedule data:', scheduleData.length, 'items')

    // Apply client-side filters
    console.log('Filtering by instructor ID:', instructorInfo.value.id)
    scheduleData = scheduleData.filter(s => {
      console.log('Schedule instructor:', s.instructor, 'vs target:', instructorInfo.value.id)
      return s.instructor === instructorInfo.value.id
    })
    console.log('After instructor filter:', scheduleData.length, 'items')

    if (filters.value.semester) {
      console.log('Filtering by semester:', filters.value.semester)
      scheduleData = scheduleData.filter(s => {
        console.log('Schedule semester:', s.semester, 'vs filter:', filters.value.semester)
        return s.semester === filters.value.semester
      })
      console.log('After semester filter:', scheduleData.length, 'items')
    }

    if (filters.value.class) {
      console.log('Filtering by class:', filters.value.class)
      scheduleData = scheduleData.filter(s => {
        // Try different field names that might contain class ID
        const classId = s.class_obj || s.class || s.class_id
        console.log('Schedule class:', classId, 'vs filter:', filters.value.class)
        return classId === filters.value.class
      })
      console.log('After class filter:', scheduleData.length, 'items')
    }

    if (filters.value.room) {
      console.log('Filtering by room:', filters.value.room)
      scheduleData = scheduleData.filter(s => {
        console.log('Schedule room:', s.room, 'vs filter:', filters.value.room)
        return s.room === filters.value.room
      })
      console.log('After room filter:', scheduleData.length, 'items')
    }

    schedules.value = scheduleData
    console.log('Final schedules loaded:', schedules.value.length)

    // Log sample schedule for debugging
    if (schedules.value.length > 0) {
      console.log('Sample schedule:', schedules.value[0])
    }
  } catch (error) {
    console.error('Error loading weekly schedules:', error)
    toast.error('Lỗi khi tải lịch tuần')
    schedules.value = []
  } finally {
    loading.value = false
  }
}

const previousWeek = () => {
  const currentDate = new Date(currentWeekStart.value + 'T00:00:00')
  currentDate.setDate(currentDate.getDate() - 7)
  currentWeekStart.value = currentDate.toISOString().split('T')[0]
  loadUpcomingSchedules()
}

const nextWeek = () => {
  const currentDate = new Date(currentWeekStart.value + 'T00:00:00')
  currentDate.setDate(currentDate.getDate() + 7)
  currentWeekStart.value = currentDate.toISOString().split('T')[0]
  loadUpcomingSchedules()
}

const goToToday = () => {
  currentWeekStart.value = getCurrentMondayString()
  loadUpcomingSchedules()
}

// Export functions
const exportToExcel = async () => {
  try {
    exporting.value = true
    const params = {
      date: currentWeekStart.value,
      instructor: instructorInfo.value.id
    }

    if (filters.value.semester) {
      params.semester = filters.value.semester
    }

    const response = await scheduleAPI.exportExcel(params)

    // Create download link
    const url = window.URL.createObjectURL(new Blob([response.data]))
    const link = document.createElement('a')
    link.href = url
    link.setAttribute('download', `LichGiangDay_${instructorInfo.value.full_name}_${currentWeekStart.value}.xlsx`)
    document.body.appendChild(link)
    link.click()
    link.remove()
    window.URL.revokeObjectURL(url)

    toast.success('Xuất file Excel thành công!')
  } catch (error) {
    console.error('Error exporting Excel:', error)
    toast.error('Lỗi khi xuất file Excel')
  } finally {
    exporting.value = false
  }
}

const exportToPdf = async () => {
  try {
    exportingPdf.value = true
    const params = {
      date: currentWeekStart.value,
      instructor: instructorInfo.value.id
    }

    if (filters.value.semester) {
      params.semester = filters.value.semester
    }

    const response = await scheduleAPI.exportPdf(params)

    // Create download link
    const url = window.URL.createObjectURL(new Blob([response.data]))
    const link = document.createElement('a')
    link.href = url
    link.setAttribute('download', `LichGiangDay_${instructorInfo.value.full_name}_${currentWeekStart.value}.pdf`)
    document.body.appendChild(link)
    link.click()
    link.remove()
    window.URL.revokeObjectURL(url)

    toast.success('Xuất file PDF thành công!')
  } catch (error) {
    console.error('Error exporting PDF:', error)
    toast.error('Lỗi khi xuất file PDF')
  } finally {
    exportingPdf.value = false
  }
}

// Utility functions
const getSchedulesForSlot = (date: string, timeSlotId: number) => {
  const filtered = schedules.value.filter(s => {
    // Try different field names for time slot
    const scheduleTimeSlot = s.time_slot_id || s.time_slot || s.timeSlot
    const match = s.schedule_date === date && scheduleTimeSlot === timeSlotId

    if (match) {
      console.log('Found schedule for slot:', {
        date,
        timeSlotId,
        schedule: s
      })
    }

    return match
  })

  return filtered
}

const getScheduleCountForDay = (date: string) => {
  return schedules.value.filter(s => s.schedule_date === date).length
}

const isToday = (date: string) => {
  const today = new Date().toISOString().split('T')[0]
  return date === today
}

const formatDisplayDate = (date: string) => {
  const d = new Date(date)
  return `${d.getDate()}/${d.getMonth() + 1}`
}

const formatDate = (date: string) => {
  return new Date(date).toLocaleDateString('vi-VN')
}

const getSessionText = (session: string) => {
  const sessionMap = {
    'SANG': 'Sáng',
    'CHIEU': 'Chiều',
    'TOI': 'Tối'
  }
  return sessionMap[session] || session
}

const getStatusColor = (status: string) => {
  switch (status) {
    case 'SCHEDULED': return 'primary'
    case 'COMPLETED': return 'success'
    case 'CANCELLED': return 'error'
    default: return 'grey'
  }
}

const getStatusText = (status: string) => {
  switch (status) {
    case 'SCHEDULED': return 'Đã lên lịch'
    case 'COMPLETED': return 'Đã hoàn thành'
    case 'CANCELLED': return 'Đã hủy'
    default: return status
  }
}

const getScheduleClass = (schedule: any) => {
  return `schedule-${schedule.status.toLowerCase()}`
}

const showScheduleDetails = (schedule: any) => {
  selectedSchedule.value = schedule
  detailsDialog.value = true
}

const handleProfileCreated = () => {
  toast.success('Tạo hồ sơ giảng viên thành công!')
  loadInstructorInfo()
}

// Additional utility methods for new dashboard
const getCurrentWeekRange = () => {
  const start = new Date(currentWeekStart.value + 'T00:00:00')
  const end = new Date(currentWeekStart.value + 'T00:00:00')
  end.setDate(end.getDate() + 6)
  return `${start.getDate()}/${start.getMonth() + 1} - ${end.getDate()}/${end.getMonth() + 1}`
}

const getSessionColor = (session: string) => {
  switch (session) {
    case 'SANG': return 'orange'
    case 'CHIEU': return 'blue'
    case 'TOI': return 'indigo'
    default: return 'grey'
  }
}

const formatScheduleTime = (schedule: any) => {
  const date = new Date(schedule.schedule_date)
  const dayNames = ['CN', 'T2', 'T3', 'T4', 'T5', 'T6', 'T7']
  const dayName = dayNames[date.getDay()]
  return `${dayName}, ${date.getDate()}/${date.getMonth() + 1} - ${schedule.time_slot_start || ''}`
}

const formatScheduleDateTime = (schedule: any) => {
  const date = new Date(schedule.schedule_date)
  const dayNames = ['Chủ nhật', 'Thứ 2', 'Thứ 3', 'Thứ 4', 'Thứ 5', 'Thứ 6', 'Thứ 7']
  const dayName = dayNames[date.getDay()]
  return `${dayName}, ${date.getDate()}/${date.getMonth() + 1} | ${schedule.time_slot_start || ''} - ${schedule.time_slot_end || ''}`
}
</script>

<style scoped>
/* Welcome Card */
.welcome-card {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
  border-radius: 16px !important;
  box-shadow: 0 8px 32px rgba(102, 126, 234, 0.3) !important;
}

/* Statistics Cards */
.stat-card {
  border-radius: 16px;
  transition: transform 0.2s, box-shadow 0.2s;
  border: none !important;
}

.stat-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
}

/* Teaching Hours Chart */
.teaching-hours-chart {
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  border-radius: 12px;
  padding: 20px;
  margin: 16px 0;
}

/* Data Table Styling */
.v-data-table {
  border-radius: 12px;
  overflow: hidden;
}

.v-data-table .v-data-table__tr:hover {
  background-color: rgba(102, 126, 234, 0.05);
}

/* Schedule List Items */
.v-list-item {
  border-radius: 8px;
  margin-bottom: 8px;
  transition: all 0.2s;
}

.v-list-item:hover {
  background-color: rgba(102, 126, 234, 0.05);
  transform: translateX(4px);
}

/* Buttons */
.v-btn {
  border-radius: 8px;
  text-transform: none;
  font-weight: 500;
}

/* Cards */
.v-card {
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
  transition: box-shadow 0.2s;
}

.v-card:hover {
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.12);
}

/* Progress Bar */
.v-progress-linear {
  border-radius: 10px;
  overflow: hidden;
}

/* Chips */
.v-chip {
  border-radius: 6px;
  font-weight: 500;
}

/* Dialog */
.v-dialog .v-card {
  border-radius: 16px;
}

/* Responsive Design */
@media (max-width: 768px) {
  .welcome-card .v-card-text {
    padding: 16px !important;
  }

  .stat-card .v-card-text {
    padding: 16px !important;
  }

  .teaching-hours-chart {
    padding: 12px;
    margin: 8px 0;
  }

  .text-h3 {
    font-size: 1.8rem !important;
  }

  .text-h4 {
    font-size: 1.5rem !important;
  }
}

/* Animation */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.v-card {
  animation: fadeInUp 0.3s ease-out;
}

/* Fullscreen Calendar Styles */
.calendar-container-fullscreen {
  overflow: auto;
  height: calc(100vh - 64px); /* Subtract toolbar height */
  padding: 16px;
}

.calendar-grid-fullscreen {
  display: grid;
  grid-template-columns: 180px repeat(7, 1fr);
  gap: 1px;
  background-color: #e0e0e0;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  overflow: hidden;
  min-height: calc(100vh - 120px);
}

.time-header-fullscreen,
.day-header-fullscreen,
.time-slot-label-fullscreen,
.calendar-cell-fullscreen {
  background-color: white;
  padding: 12px;
  min-height: 120px;
}

.time-header-fullscreen {
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  background-color: #f5f5f5;
  font-size: 16px;
}

.day-header-fullscreen {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  background-color: #f5f5f5;
  text-align: center;
}

.day-header-fullscreen.today {
  background-color: #e3f2fd;
  color: #1976d2;
}

.day-header-fullscreen .day-name {
  font-size: 16px;
  margin-bottom: 6px;
}

.day-header-fullscreen .day-date {
  font-size: 14px;
  color: #666;
  margin-bottom: 4px;
}

.day-header-fullscreen .day-count {
  font-size: 12px;
  color: #999;
}

.time-slot-label-fullscreen {
  display: flex;
  flex-direction: column;
  justify-content: center;
  background-color: #fafafa;
  border-right: 2px solid #e0e0e0;
  text-align: center;
}

.time-slot-label-fullscreen .slot-name {
  font-weight: bold;
  font-size: 14px;
  margin-bottom: 6px;
}

.time-slot-label-fullscreen .slot-time {
  font-size: 13px;
  color: #666;
  margin-bottom: 4px;
}

.time-slot-label-fullscreen .slot-session {
  font-size: 12px;
  color: #999;
}

.calendar-cell-fullscreen {
  position: relative;
  min-height: 120px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.calendar-cell-fullscreen:hover {
  background-color: #f8f9fa;
}

.calendar-cell-fullscreen.today {
  background-color: #f3e5f5;
}

.schedule-item-fullscreen {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 10px;
  border-radius: 8px;
  margin-bottom: 6px;
  font-size: 12px;
  line-height: 1.4;
  cursor: pointer;
  transition: transform 0.2s, box-shadow 0.2s;
  position: relative;
  overflow: hidden;
}

.schedule-item-fullscreen:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.15);
}

.schedule-item-fullscreen:last-child {
  margin-bottom: 0;
}

.schedule-item-fullscreen .schedule-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 6px;
}

.schedule-item-fullscreen .schedule-class {
  font-weight: bold;
  margin-bottom: 3px;
  font-size: 13px;
}

.schedule-item-fullscreen .schedule-subject {
  font-size: 11px;
  margin-bottom: 3px;
  opacity: 0.9;
}

.schedule-item-fullscreen .schedule-room,
.schedule-item-fullscreen .schedule-instructor {
  font-size: 10px;
  opacity: 0.8;
  display: flex;
  align-items: center;
  margin-bottom: 2px;
}

.empty-slot-fullscreen {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: #ccc;
  font-size: 14px;
}

.empty-slot-fullscreen .empty-text {
  margin-top: 6px;
  font-size: 12px;
}

/* Schedule status colors for fullscreen */
.schedule-item-fullscreen.schedule-scheduled {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.schedule-item-fullscreen.schedule-completed {
  background: linear-gradient(135deg, #4caf50 0%, #45a049 100%);
}

.schedule-item-fullscreen.schedule-cancelled {
  background: linear-gradient(135deg, #f44336 0%, #d32f2f 100%);
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
</style>
