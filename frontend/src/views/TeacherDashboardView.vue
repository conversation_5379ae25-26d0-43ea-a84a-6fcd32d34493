<template>
  <div>
    <!-- No Instructor Profile Alert -->
    <v-alert
      v-if="!instructorInfo && !loadingInstructor && hasTriedLoadingInstructor"
      type="warning"
      variant="tonal"
      class="mb-6"
      prominent
    >
      <template v-slot:prepend>
        <v-icon size="32">mdi-account-alert</v-icon>
      </template>

      <v-alert-title class="text-h6 mb-2">
        Chưa có hồ sơ giảng viên
      </v-alert-title>

      <div class="mb-4">
        Bạn chưa có hồ sơ giảng viên trong hệ thống. <PERSON><PERSON> xem lịch giảng dạy, bạn cần có hồ sơ giảng viên.
      </div>

      <div class="d-flex flex-wrap gap-3">
        <v-btn
          color="primary"
          variant="elevated"
          prepend-icon="mdi-account-plus"
          @click="showCreateProfileDialog = true"
        >
          Tạo hồ sơ giảng viên
        </v-btn>

        <v-btn
          color="info"
          variant="outlined"
          prepend-icon="mdi-refresh"
          @click="loadInstructorInfo"
          :loading="loadingInstructor"
        >
          Kiểm tra lại
        </v-btn>

        <v-btn
          color="warning"
          variant="text"
          prepend-icon="mdi-help-circle"
          @click="showHelpDialog = true"
        >
          Cần hỗ trợ?
        </v-btn>
      </div>
    </v-alert>

    <!-- Header -->
    <v-row v-if="instructorInfo">
      <v-col cols="12">
        <div class="d-flex justify-space-between align-center mb-4">
          <h1 class="text-h4">Lịch giảng dạy của tôi</h1>
          <div class="d-flex align-center">
            <v-chip color="primary" class="mr-2">
              <v-icon start>mdi-account-tie</v-icon>
              {{ instructorInfo.full_name }}
            </v-chip>
            <v-chip color="info">
              <v-icon start>mdi-card-account-details</v-icon>
              {{ instructorInfo.employee_code }}
            </v-chip>
          </div>
        </div>
      </v-col>
    </v-row>

    <!-- Filters -->
    <v-card class="mb-4" v-if="instructorInfo">
      <v-card-title>Bộ lọc</v-card-title>
      <v-card-text>
        <v-row>
          <v-col cols="12" md="3">
            <v-select
              v-model="filters.academicYear"
              :items="academicYears"
              item-title="year_code"
              item-value="id"
              label="Năm học"
              clearable
              @update:model-value="onAcademicYearChange"
            ></v-select>
          </v-col>
          <v-col cols="12" md="3">
            <v-select
              v-model="filters.semester"
              :items="filteredSemesters"
              item-title="name"
              item-value="id"
              label="Học kỳ"
              clearable
              @update:model-value="loadWeeklySchedules"
            ></v-select>
          </v-col>
          <v-col cols="12" md="3">
            <v-select
              v-model="filters.class"
              :items="classes"
              item-title="name"
              item-value="id"
              label="Lớp học"
              clearable
              @update:model-value="loadWeeklySchedules"
            ></v-select>
          </v-col>
          <v-col cols="12" md="3">
            <v-select
              v-model="filters.room"
              :items="rooms"
              item-title="name"
              item-value="id"
              label="Phòng học"
              clearable
              @update:model-value="loadWeeklySchedules"
            ></v-select>
          </v-col>
        </v-row>
      </v-card-text>
    </v-card>

    <!-- Calendar -->
    <v-card v-if="instructorInfo">
      <v-card-title>
        <v-row align="center">
          <v-col cols="auto">
            <v-btn icon @click="previousWeek" :loading="loading">
              <v-icon>mdi-chevron-left</v-icon>
            </v-btn>
          </v-col>
          <v-col>
            <h3>{{ weekTitle }}</h3>
          </v-col>
          <v-col cols="auto">
            <v-btn icon @click="nextWeek" :loading="loading">
              <v-icon>mdi-chevron-right</v-icon>
            </v-btn>
          </v-col>
          <v-col cols="auto">
            <v-btn color="primary" @click="goToToday" :loading="loading">
              Hôm nay
            </v-btn>
          </v-col>
          <v-col cols="auto">
            <v-menu>
              <template v-slot:activator="{ props }">
                <v-btn color="success" v-bind="props" :loading="exporting" prepend-icon="mdi-file-excel">
                  Xuất Excel
                  <v-icon>mdi-chevron-down</v-icon>
                </v-btn>
              </template>
              <v-list>
                <v-list-item @click="exportToExcel">
                  <v-list-item-title>Mẫu theo giảng viên</v-list-item-title>
                </v-list-item>
              </v-list>
            </v-menu>
          </v-col>
          <v-col cols="auto">
            <v-menu>
              <template v-slot:activator="{ props }">
                <v-btn color="error" v-bind="props" :loading="exportingPdf" prepend-icon="mdi-file-pdf-box">
                  Xuất PDF
                  <v-icon>mdi-chevron-down</v-icon>
                </v-btn>
              </template>
              <v-list>
                <v-list-item @click="exportToPdf">
                  <v-list-item-title>Mẫu theo giảng viên</v-list-item-title>
                </v-list-item>
              </v-list>
            </v-menu>
          </v-col>
          <v-col cols="auto">
            <v-btn color="info" prepend-icon="mdi-refresh" @click="loadWeeklySchedules">
              Làm mới
            </v-btn>
          </v-col>
        </v-row>
      </v-card-title>

      <v-card-text>
        <v-overlay :model-value="loading" class="align-center justify-center">
          <v-progress-circular
            color="primary"
            indeterminate
            size="64"
          ></v-progress-circular>
        </v-overlay>

        <div class="calendar-container">
          <div class="calendar-grid">
            <!-- Time slots header -->
            <div class="time-header">
              <div class="header-content">
                <div class="time-label">Thời gian</div>
              </div>
            </div>
            <div v-for="day in weekDays" :key="day.date" class="day-header" :class="{ 'today': isToday(day.date) }">
              <div class="day-name">{{ day.name }}</div>
              <div class="day-date">{{ formatDisplayDate(day.date) }}</div>
              <div class="day-count">{{ getScheduleCountForDay(day.date) }} lịch</div>
            </div>

            <!-- Calendar body -->
            <template v-for="timeSlot in timeSlots" :key="timeSlot.id">
              <div class="time-slot-label">
                <div class="slot-name">{{ timeSlot.slot_name }}</div>
                <div class="slot-time">{{ timeSlot.start_time }} - {{ timeSlot.end_time }}</div>
                <div class="slot-session">{{ getSessionText(timeSlot.session) }}</div>
              </div>

              <div v-for="day in weekDays" :key="`${timeSlot.id}-${day.date}`"
                   class="calendar-cell"
                   :class="{ 'today': isToday(day.date) }">
                <div
                  v-for="schedule in getSchedulesForSlot(day.date, timeSlot.id)"
                  :key="schedule.id"
                  class="schedule-item"
                  :class="getScheduleClass(schedule)"
                  @click="showScheduleDetails(schedule)"
                >
                  <div class="schedule-header">
                    <v-chip size="x-small" :color="schedule.lesson_type === 'LT' ? 'blue' : 'green'">
                      {{ schedule.lesson_type }}
                    </v-chip>
                    <v-chip size="x-small" :color="getStatusColor(schedule.status)">
                      {{ getStatusText(schedule.status) }}
                    </v-chip>
                  </div>
                  <div class="schedule-class">{{ schedule.class_code }}</div>
                  <div class="schedule-subject">{{ schedule.subject_code }}</div>
                  <div class="schedule-room">
                    <v-icon size="12">mdi-door</v-icon>
                    {{ schedule.room_code }}
                  </div>
                  <div class="schedule-instructor">
                    <v-icon size="12">mdi-account</v-icon>
                    {{ schedule.instructor_name }}
                  </div>
                </div>

                <!-- Empty slot indicator -->
                <div v-if="getSchedulesForSlot(day.date, timeSlot.id).length === 0" class="empty-slot">
                  <v-icon color="grey-lighten-2">mdi-calendar-blank</v-icon>
                  <span class="empty-text">Trống</span>
                </div>
              </div>
            </template>
          </div>
        </div>
      </v-card-text>
    </v-card>

    <!-- Create Profile Dialog -->
    <CreateInstructorProfileDialog
      v-model="showCreateProfileDialog"
      @success="handleProfileCreated"
    />

    <!-- Help Dialog -->
    <HelpDialog
      v-model="showHelpDialog"
      @create-profile="showCreateProfileDialog = true"
    />

    <!-- Schedule Details Dialog -->
    <v-dialog v-model="detailsDialog" max-width="700px">
      <v-card v-if="selectedSchedule">
        <v-card-title class="d-flex align-center">
          <v-icon class="mr-2">mdi-calendar-clock</v-icon>
          Chi tiết lịch giảng dạy
          <v-spacer></v-spacer>
          <v-chip :color="getStatusColor(selectedSchedule.status)" size="small">
            {{ getStatusText(selectedSchedule.status) }}
          </v-chip>
        </v-card-title>
        <v-divider></v-divider>
        <v-card-text>
          <v-row>
            <v-col cols="12" md="6">
              <v-list-item>
                <v-list-item-title>
                  <v-icon class="mr-2">mdi-calendar</v-icon>
                  Ngày học
                </v-list-item-title>
                <v-list-item-subtitle>{{ formatDate(selectedSchedule.schedule_date) }}</v-list-item-subtitle>
              </v-list-item>
            </v-col>
            <v-col cols="12" md="6">
              <v-list-item>
                <v-list-item-title>
                  <v-icon class="mr-2">mdi-clock</v-icon>
                  Ca học
                </v-list-item-title>
                <v-list-item-subtitle>{{ selectedSchedule.time_slot_name }} ({{ selectedSchedule.time_slot_start }} - {{ selectedSchedule.time_slot_end }})</v-list-item-subtitle>
              </v-list-item>
            </v-col>
            <v-col cols="12" md="6">
              <v-list-item>
                <v-list-item-title>
                  <v-icon class="mr-2">mdi-google-classroom</v-icon>
                  Lớp học
                </v-list-item-title>
                <v-list-item-subtitle>{{ selectedSchedule.class_name }} ({{ selectedSchedule.class_code }})</v-list-item-subtitle>
              </v-list-item>
            </v-col>
            <v-col cols="12" md="6">
              <v-list-item>
                <v-list-item-title>
                  <v-icon class="mr-2">mdi-book-open-variant</v-icon>
                  Môn học
                </v-list-item-title>
                <v-list-item-subtitle>{{ selectedSchedule.subject_name }} ({{ selectedSchedule.subject_code }})</v-list-item-subtitle>
              </v-list-item>
            </v-col>
            <v-col cols="12" md="6">
              <v-list-item>
                <v-list-item-title>
                  <v-icon class="mr-2">mdi-book</v-icon>
                  Bài học
                </v-list-item-title>
                <v-list-item-subtitle>{{ selectedSchedule.lesson_title }}</v-list-item-subtitle>
              </v-list-item>
            </v-col>
            <v-col cols="12" md="6">
              <v-list-item>
                <v-list-item-title>
                  <v-icon class="mr-2">mdi-door</v-icon>
                  Phòng học
                </v-list-item-title>
                <v-list-item-subtitle>{{ selectedSchedule.room_name }} ({{ selectedSchedule.room_code }})</v-list-item-subtitle>
              </v-list-item>
            </v-col>
            <v-col cols="12" md="6">
              <v-list-item>
                <v-list-item-title>
                  <v-icon class="mr-2">mdi-format-list-bulleted-type</v-icon>
                  Loại bài học
                </v-list-item-title>
                <v-list-item-subtitle>
                  <v-chip :color="selectedSchedule.lesson_type === 'LT' ? 'blue' : 'green'" size="small">
                    {{ selectedSchedule.lesson_type === 'LT' ? 'Lý thuyết' : 'Thực hành' }}
                  </v-chip>
                </v-list-item-subtitle>
              </v-list-item>
            </v-col>
            <v-col cols="12" md="6">
              <v-list-item>
                <v-list-item-title>
                  <v-icon class="mr-2">mdi-timer</v-icon>
                  Thời lượng
                </v-list-item-title>
                <v-list-item-subtitle>{{ selectedSchedule.duration_hours }} giờ</v-list-item-subtitle>
              </v-list-item>
            </v-col>
            <v-col cols="12" v-if="selectedSchedule.practice_group_number">
              <v-list-item>
                <v-list-item-title>
                  <v-icon class="mr-2">mdi-account-group</v-icon>
                  Nhóm thực hành
                </v-list-item-title>
                <v-list-item-subtitle>Nhóm {{ selectedSchedule.practice_group_number }}</v-list-item-subtitle>
              </v-list-item>
            </v-col>
            <v-col cols="12" v-if="selectedSchedule.notes">
              <v-list-item>
                <v-list-item-title>
                  <v-icon class="mr-2">mdi-note-text</v-icon>
                  Ghi chú
                </v-list-item-title>
                <v-list-item-subtitle>{{ selectedSchedule.notes }}</v-list-item-subtitle>
              </v-list-item>
            </v-col>
          </v-row>
        </v-card-text>
        <v-divider></v-divider>
        <v-card-actions>
          <v-spacer></v-spacer>
          <v-btn text @click="detailsDialog = false">Đóng</v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import {
  scheduleAPI,
  timeSlotAPI,
  academicYearAPI,
  semesterAPI,
  instructorAPI,
  classAPI,
  roomAPI
} from '../services/api'
import { useAuthStore } from '@/stores/auth'
import { toast } from '@/composables/useToast'
import CreateInstructorProfileDialog from '@/components/CreateInstructorProfileDialog.vue'
import HelpDialog from '@/components/HelpDialog.vue'

const authStore = useAuthStore()

const loading = ref(false)
const loadingInstructor = ref(false)
const hasTriedLoadingInstructor = ref(false)
const exporting = ref(false)
const exportingPdf = ref(false)

// Use string date for better reactivity
const getCurrentMondayString = () => {
  const today = new Date()
  const monday = new Date(today)
  monday.setDate(monday.getDate() - monday.getDay() + 1)
  return monday.toISOString().split('T')[0]
}

const currentWeekStart = ref(getCurrentMondayString())
const schedules = ref([])
const timeSlots = ref([])
const academicYears = ref([])
const semesters = ref([])
const allSemesters = ref([])
const classes = ref([])
const rooms = ref([])
const instructorInfo = ref(null)
const detailsDialog = ref(false)
const selectedSchedule = ref(null)
const showCreateProfileDialog = ref(false)
const showHelpDialog = ref(false)

const filters = ref({
  academicYear: null,
  semester: null,
  instructor: null, // This will be locked to current instructor
  class: null,
  room: null
})

// Computed property for filtered semesters based on academic year
const filteredSemesters = computed(() => {
  if (!filters.value.academicYear) {
    return allSemesters.value
  }
  return allSemesters.value.filter((s: any) => s.academic_year === filters.value.academicYear)
})

const weekDays = computed(() => {
  const days = []
  const dayNames = ['Thứ 2', 'Thứ 3', 'Thứ 4', 'Thứ 5', 'Thứ 6', 'Thứ 7', 'Chủ nhật']

  for (let i = 0; i < 7; i++) {
    const date = new Date(currentWeekStart.value + 'T00:00:00')
    date.setDate(date.getDate() + i)
    days.push({
      name: dayNames[i],
      date: date.toISOString().split('T')[0]
    })
  }
  return days
})

const weekTitle = computed(() => {
  const start = new Date(currentWeekStart.value + 'T00:00:00')
  const end = new Date(currentWeekStart.value + 'T00:00:00')
  end.setDate(end.getDate() + 6)

  return `${start.toLocaleDateString('vi-VN')} - ${end.toLocaleDateString('vi-VN')}`
})

// Load instructor info first
const loadInstructorInfo = async () => {
  try {
    loadingInstructor.value = true
    hasTriedLoadingInstructor.value = true
    const response = await instructorAPI.myProfile()
    instructorInfo.value = response.data

    // Lock instructor filter to current instructor
    filters.value.instructor = instructorInfo.value.id

    // Load initial data after getting instructor info
    await loadInitialData()
    await loadWeeklySchedules()
  } catch (error: any) {
    console.error('Error loading instructor info:', error)
    if (error.response?.status === 404) {
      instructorInfo.value = null
    } else {
      toast.error('Lỗi khi tải thông tin giảng viên')
    }
  } finally {
    loadingInstructor.value = false
  }
}

onMounted(async () => {
  await loadInstructorInfo()
})

const loadInitialData = async () => {
  try {
    const [timeSlotsRes, academicYearsRes, semestersRes, classesRes, roomsRes] = await Promise.all([
      timeSlotAPI.list(),
      academicYearAPI.list(),
      semesterAPI.list(),
      classAPI.list(),
      roomAPI.list()
    ])

    timeSlots.value = timeSlotsRes.data.results || timeSlotsRes.data
    academicYears.value = academicYearsRes.data.results || academicYearsRes.data
    allSemesters.value = semestersRes.data.results || semestersRes.data
    classes.value = classesRes.data.results || classesRes.data
    rooms.value = roomsRes.data.results || roomsRes.data
  } catch (error) {
    console.error('Error loading initial data:', error)
    toast.error('Lỗi khi tải dữ liệu ban đầu')
  }
}

const onAcademicYearChange = () => {
  filters.value.semester = null
  loadWeeklySchedules()
}

const loadWeeklySchedules = async () => {
  if (!instructorInfo.value) {
    return
  }

  loading.value = true
  try {
    const dateString = currentWeekStart.value
    console.log('Loading weekly schedules for date:', dateString)

    const params = {
      date: dateString,
      instructor: instructorInfo.value.id, // Always filter by current instructor
      ...Object.fromEntries(
        Object.entries(filters.value).filter(([key, value]) =>
          key !== 'instructor' && value !== null && value !== ''
        )
      )
    }

    const response = await scheduleAPI.weekly(params.date)
    let scheduleData = response.data.schedules || []

    // Apply client-side filters
    scheduleData = scheduleData.filter(s => s.instructor === instructorInfo.value.id)

    if (filters.value.semester) {
      scheduleData = scheduleData.filter(s => s.semester === filters.value.semester)
    }
    if (filters.value.class) {
      scheduleData = scheduleData.filter(s => s.class === filters.value.class)
    }
    if (filters.value.room) {
      scheduleData = scheduleData.filter(s => s.room === filters.value.room)
    }

    schedules.value = scheduleData
    console.log('Loaded schedules:', schedules.value.length)
  } catch (error) {
    console.error('Error loading weekly schedules:', error)
    toast.error('Lỗi khi tải lịch tuần')
  } finally {
    loading.value = false
  }
}

const previousWeek = () => {
  const currentDate = new Date(currentWeekStart.value + 'T00:00:00')
  currentDate.setDate(currentDate.getDate() - 7)
  currentWeekStart.value = currentDate.toISOString().split('T')[0]
  loadWeeklySchedules()
}

const nextWeek = () => {
  const currentDate = new Date(currentWeekStart.value + 'T00:00:00')
  currentDate.setDate(currentDate.getDate() + 7)
  currentWeekStart.value = currentDate.toISOString().split('T')[0]
  loadWeeklySchedules()
}

const goToToday = () => {
  currentWeekStart.value = getCurrentMondayString()
  loadWeeklySchedules()
}

// Export functions
const exportToExcel = async () => {
  try {
    exporting.value = true
    const params = {
      date: currentWeekStart.value,
      instructor: instructorInfo.value.id
    }

    if (filters.value.semester) {
      params.semester = filters.value.semester
    }

    const response = await scheduleAPI.exportExcel(params)

    // Create download link
    const url = window.URL.createObjectURL(new Blob([response.data]))
    const link = document.createElement('a')
    link.href = url
    link.setAttribute('download', `LichGiangDay_${instructorInfo.value.full_name}_${currentWeekStart.value}.xlsx`)
    document.body.appendChild(link)
    link.click()
    link.remove()
    window.URL.revokeObjectURL(url)

    toast.success('Xuất file Excel thành công!')
  } catch (error) {
    console.error('Error exporting Excel:', error)
    toast.error('Lỗi khi xuất file Excel')
  } finally {
    exporting.value = false
  }
}

const exportToPdf = async () => {
  try {
    exportingPdf.value = true
    const params = {
      date: currentWeekStart.value,
      instructor: instructorInfo.value.id
    }

    if (filters.value.semester) {
      params.semester = filters.value.semester
    }

    const response = await scheduleAPI.exportPdf(params)

    // Create download link
    const url = window.URL.createObjectURL(new Blob([response.data]))
    const link = document.createElement('a')
    link.href = url
    link.setAttribute('download', `LichGiangDay_${instructorInfo.value.full_name}_${currentWeekStart.value}.pdf`)
    document.body.appendChild(link)
    link.click()
    link.remove()
    window.URL.revokeObjectURL(url)

    toast.success('Xuất file PDF thành công!')
  } catch (error) {
    console.error('Error exporting PDF:', error)
    toast.error('Lỗi khi xuất file PDF')
  } finally {
    exportingPdf.value = false
  }
}

// Utility functions
const getSchedulesForSlot = (date: string, timeSlotId: number) => {
  return schedules.value.filter(s =>
    s.schedule_date === date && s.time_slot_id === timeSlotId
  )
}

const getScheduleCountForDay = (date: string) => {
  return schedules.value.filter(s => s.schedule_date === date).length
}

const isToday = (date: string) => {
  const today = new Date().toISOString().split('T')[0]
  return date === today
}

const formatDisplayDate = (date: string) => {
  const d = new Date(date)
  return `${d.getDate()}/${d.getMonth() + 1}`
}

const formatDate = (date: string) => {
  return new Date(date).toLocaleDateString('vi-VN')
}

const getSessionText = (session: string) => {
  const sessionMap = {
    'SANG': 'Sáng',
    'CHIEU': 'Chiều',
    'TOI': 'Tối'
  }
  return sessionMap[session] || session
}

const getStatusColor = (status: string) => {
  switch (status) {
    case 'SCHEDULED': return 'primary'
    case 'COMPLETED': return 'success'
    case 'CANCELLED': return 'error'
    default: return 'grey'
  }
}

const getStatusText = (status: string) => {
  switch (status) {
    case 'SCHEDULED': return 'Đã lên lịch'
    case 'COMPLETED': return 'Đã hoàn thành'
    case 'CANCELLED': return 'Đã hủy'
    default: return status
  }
}

const getScheduleClass = (schedule: any) => {
  return `schedule-${schedule.status.toLowerCase()}`
}

const showScheduleDetails = (schedule: any) => {
  selectedSchedule.value = schedule
  detailsDialog.value = true
}

const handleProfileCreated = () => {
  toast.success('Tạo hồ sơ giảng viên thành công!')
  loadInstructorInfo()
}
</script>

<style scoped>
.calendar-container {
  overflow-x: auto;
}

.calendar-grid {
  display: grid;
  grid-template-columns: 150px repeat(7, 1fr);
  gap: 1px;
  background-color: #e0e0e0;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  overflow: hidden;
}

.time-header,
.day-header,
.time-slot-label,
.calendar-cell {
  background-color: white;
  padding: 8px;
  min-height: 80px;
}

.time-header {
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  background-color: #f5f5f5;
}

.day-header {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  background-color: #f5f5f5;
  text-align: center;
}

.day-header.today {
  background-color: #e3f2fd;
  color: #1976d2;
}

.day-name {
  font-size: 14px;
  margin-bottom: 4px;
}

.day-date {
  font-size: 12px;
  color: #666;
  margin-bottom: 2px;
}

.day-count {
  font-size: 10px;
  color: #999;
}

.time-slot-label {
  display: flex;
  flex-direction: column;
  justify-content: center;
  background-color: #fafafa;
  border-right: 2px solid #e0e0e0;
  text-align: center;
}

.slot-name {
  font-weight: bold;
  font-size: 12px;
  margin-bottom: 4px;
}

.slot-time {
  font-size: 11px;
  color: #666;
  margin-bottom: 2px;
}

.slot-session {
  font-size: 10px;
  color: #999;
}

.calendar-cell {
  position: relative;
  min-height: 100px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.calendar-cell:hover {
  background-color: #f8f9fa;
}

.calendar-cell.today {
  background-color: #f3e5f5;
}

.schedule-item {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 8px;
  border-radius: 6px;
  margin-bottom: 4px;
  font-size: 11px;
  line-height: 1.3;
  cursor: pointer;
  transition: transform 0.2s, box-shadow 0.2s;
  position: relative;
  overflow: hidden;
}

.schedule-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.schedule-item:last-child {
  margin-bottom: 0;
}

.schedule-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 4px;
}

.schedule-class {
  font-weight: bold;
  margin-bottom: 2px;
}

.schedule-subject {
  font-size: 10px;
  margin-bottom: 2px;
  opacity: 0.9;
}

.schedule-room,
.schedule-instructor {
  font-size: 9px;
  opacity: 0.8;
  display: flex;
  align-items: center;
  margin-bottom: 1px;
}

.empty-slot {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: #ccc;
  font-size: 12px;
}

.empty-text {
  margin-top: 4px;
  font-size: 10px;
}

/* Schedule status colors */
.schedule-scheduled {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.schedule-completed {
  background: linear-gradient(135deg, #4caf50 0%, #45a049 100%);
}

.schedule-cancelled {
  background: linear-gradient(135deg, #f44336 0%, #d32f2f 100%);
}

/* Responsive design */
@media (max-width: 768px) {
  .calendar-grid {
    grid-template-columns: 120px repeat(7, minmax(80px, 1fr));
  }

  .time-slot-label {
    padding: 4px;
  }

  .slot-name {
    font-size: 10px;
  }

  .slot-time {
    font-size: 9px;
  }

  .schedule-item {
    padding: 4px;
    font-size: 9px;
  }
}
</style>
