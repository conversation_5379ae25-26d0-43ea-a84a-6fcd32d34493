<template>
  <div>
    <v-row>
      <v-col cols="12">
        <h1 class="text-h4 mb-4">Lịch gi<PERSON>ng dạy</h1>
      </v-col>
    </v-row>

    <!-- Filters -->
    <v-card class="mb-4">
      <v-card-title>Bộ lọc</v-card-title>
      <v-card-text>
        <v-row>
          <v-col cols="12" md="2">
            <v-select
              v-model="filters.academicYear"
              :items="academicYears"
              item-title="year_code"
              item-value="id"
              label="Năm học"
              clearable
              @update:model-value="onAcademicYearChange"
            ></v-select>
          </v-col>
          <v-col cols="12" md="2">
            <v-select
              v-model="filters.semester"
              :items="filteredSemesters"
              item-title="name"
              item-value="id"
              label="Học kỳ"
              clearable
              @update:model-value="loadWeeklySchedules"
            ></v-select>
          </v-col>
          <v-col cols="12" md="3">
            <v-select
              v-model="filters.instructor"
              :items="instructors"
              item-title="full_name"
              item-value="id"
              label="Giảng viên"
              clearable
              @update:model-value="loadWeeklySchedules"
            ></v-select>
          </v-col>
          <v-col cols="12" md="3">
            <v-select
              v-model="filters.class"
              :items="classes"
              item-title="name"
              item-value="id"
              label="Lớp học"
              clearable
              @update:model-value="loadWeeklySchedules"
            ></v-select>
          </v-col>
          <v-col cols="12" md="3">
            <v-select
              v-model="filters.room"
              :items="rooms"
              item-title="name"
              item-value="id"
              label="Phòng học"
              clearable
              @update:model-value="loadWeeklySchedules"
            ></v-select>
          </v-col>
        </v-row>
      </v-card-text>
    </v-card>

    <!-- Calendar -->
    <v-card>
      <v-card-title>
        <v-row align="center">
          <v-col cols="auto">
            <v-btn icon @click="previousWeek" :loading="loading">
              <v-icon>mdi-chevron-left</v-icon>
            </v-btn>
          </v-col>
          <v-col>
            <h3>{{ weekTitle }}</h3>
          </v-col>
          <v-col cols="auto">
            <v-btn icon @click="nextWeek" :loading="loading">
              <v-icon>mdi-chevron-right</v-icon>
            </v-btn>
          </v-col>
          <v-col cols="auto">
            <v-btn color="primary" @click="goToToday" :loading="loading">
              Hôm nay
            </v-btn>
          </v-col>
          <v-col cols="auto">
            <v-menu>
              <template v-slot:activator="{ props }">
                <v-btn color="success" v-bind="props" :loading="exporting" prepend-icon="mdi-file-excel">
                  Xuất Excel
                  <v-icon>mdi-chevron-down</v-icon>
                </v-btn>
              </template>
              <v-list>
                <v-list-item @click="exportToExcel">
                  <v-list-item-title>Mẫu theo giảng viên</v-list-item-title>
                </v-list-item>
                <v-list-item @click="exportCampusWideExcel">
                  <v-list-item-title>Mẫu toàn trường</v-list-item-title>
                </v-list-item>
                <v-list-item @click="exportClassExcel">
                  <v-list-item-title>Mẫu theo lớp</v-list-item-title>
                </v-list-item>
                <v-list-item @click="exportRoomStatisticsExcel">
                  <v-list-item-title>Thống kê phòng học</v-list-item-title>
                </v-list-item>
              </v-list>
            </v-menu>
          </v-col>
          <v-col cols="auto">
            <v-menu>
              <template v-slot:activator="{ props }">
                <v-btn color="error" v-bind="props" :loading="exportingPdf" prepend-icon="mdi-file-pdf-box">
                  Xuất PDF
                  <v-icon>mdi-chevron-down</v-icon>
                </v-btn>
              </template>
              <v-list>
                <v-list-item @click="exportToPdf">
                  <v-list-item-title>Mẫu theo giảng viên</v-list-item-title>
                </v-list-item>
                <v-list-item @click="exportCampusWidePdf">
                  <v-list-item-title>Mẫu toàn trường</v-list-item-title>
                </v-list-item>
                <v-list-item @click="exportClassPdf">
                  <v-list-item-title>Mẫu theo lớp</v-list-item-title>
                </v-list-item>
                <v-list-item @click="exportRoomStatisticsPdf">
                  <v-list-item-title>Thống kê phòng học</v-list-item-title>
                </v-list-item>
              </v-list>
            </v-menu>
          </v-col>
          <v-col cols="auto">
            <v-btn color="success" prepend-icon="mdi-plus" to="/schedules/create">
              Tạo lịch
            </v-btn>
          </v-col>
          <v-col cols="auto">
            <v-btn color="info" prepend-icon="mdi-refresh" @click="loadWeeklySchedules">
              Làm mới
            </v-btn>
          </v-col>
        </v-row>
      </v-card-title>

      <v-card-text>
        <v-overlay :model-value="loading" class="align-center justify-center">
          <v-progress-circular
            color="primary"
            indeterminate
            size="64"
          ></v-progress-circular>
        </v-overlay>

        <div class="calendar-container">
          <div class="calendar-grid">
            <!-- Time slots header -->
            <div class="time-header">
              <div class="header-content">
                <div class="time-label">Thời gian</div>
              </div>
            </div>
            <div v-for="day in weekDays" :key="day.date" class="day-header" :class="{ 'today': isToday(day.date) }">
              <div class="day-name">{{ day.name }}</div>
              <div class="day-date">{{ formatDisplayDate(day.date) }}</div>
              <div class="day-count">{{ getScheduleCountForDay(day.date) }} lịch</div>
            </div>

            <!-- Calendar body -->
            <template v-for="timeSlot in timeSlots" :key="timeSlot.id">
              <div class="time-slot-label">
                <div class="slot-name">{{ timeSlot.slot_name }}</div>
                <div class="slot-time">{{ timeSlot.start_time }} - {{ timeSlot.end_time }}</div>
                <div class="slot-session">{{ getSessionText(timeSlot.session) }}</div>
              </div>

              <div v-for="day in weekDays" :key="`${timeSlot.id}-${day.date}`"
                   class="calendar-cell"
                   :class="{ 'today': isToday(day.date) }"
                   @click="createScheduleForSlot(day.date, timeSlot.id)">
                <div
                  v-for="schedule in getSchedulesForSlot(day.date, timeSlot.id)"
                  :key="schedule.id"
                  class="schedule-item"
                  :class="getScheduleClass(schedule)"
                  @click.stop="showScheduleDetails(schedule)"
                >
                  <div class="schedule-header">
                    <v-chip size="x-small" :color="schedule.lesson_type === 'LT' ? 'blue' : 'green'">
                      {{ schedule.lesson_type }}
                    </v-chip>
                    <v-chip size="x-small" :color="getStatusColor(schedule.status)">
                      {{ getStatusText(schedule.status) }}
                    </v-chip>
                  </div>
                  <div class="schedule-class">{{ schedule.class_code }}</div>
                  <div class="schedule-subject">{{ schedule.subject_code }}</div>
                  <div class="schedule-room">
                    <v-icon size="12">mdi-door</v-icon>
                    {{ schedule.room_code }}
                  </div>
                  <div class="schedule-instructor">
                    <v-icon size="12">mdi-account</v-icon>
                    {{ schedule.instructor_name }}
                  </div>
                  <div class="schedule-actions">
                    <v-btn icon size="x-small" @click.stop="editSchedule(schedule)">
                      <v-icon size="12">mdi-pencil</v-icon>
                    </v-btn>
                    <v-btn icon size="x-small" color="error" @click.stop="deleteSchedule(schedule)">
                      <v-icon size="12">mdi-delete</v-icon>
                    </v-btn>
                  </div>
                </div>

                <!-- Empty slot indicator -->
                <div v-if="getSchedulesForSlot(day.date, timeSlot.id).length === 0" class="empty-slot">
                  <v-icon color="grey-lighten-2">mdi-plus</v-icon>
                  <span class="empty-text">Thêm lịch</span>
                </div>
              </div>
            </template>
          </div>
        </div>
      </v-card-text>
    </v-card>

    <!-- Schedule Details Dialog -->
    <v-dialog v-model="detailsDialog" max-width="700px">
      <v-card v-if="selectedSchedule">
        <v-card-title class="d-flex align-center">
          <v-icon class="mr-2">mdi-calendar-clock</v-icon>
          Chi tiết lịch giảng dạy
          <v-spacer></v-spacer>
          <v-chip :color="getStatusColor(selectedSchedule.status)" size="small">
            {{ getStatusText(selectedSchedule.status) }}
          </v-chip>
        </v-card-title>
        <v-divider></v-divider>
        <v-card-text>
          <v-row>
            <v-col cols="12" md="6">
              <v-list-item>
                <v-list-item-title>
                  <v-icon class="mr-2">mdi-calendar</v-icon>
                  Ngày học
                </v-list-item-title>
                <v-list-item-subtitle>{{ formatDate(selectedSchedule.schedule_date) }}</v-list-item-subtitle>
              </v-list-item>
            </v-col>
            <v-col cols="12" md="6">
              <v-list-item>
                <v-list-item-title>
                  <v-icon class="mr-2">mdi-clock</v-icon>
                  Ca học
                </v-list-item-title>
                <v-list-item-subtitle>{{ selectedSchedule.time_slot_name }} ({{ selectedSchedule.time_slot_start }} - {{ selectedSchedule.time_slot_end }})</v-list-item-subtitle>
              </v-list-item>
            </v-col>
            <v-col cols="12" md="6">
              <v-list-item>
                <v-list-item-title>
                  <v-icon class="mr-2">mdi-google-classroom</v-icon>
                  Lớp học
                </v-list-item-title>
                <v-list-item-subtitle>{{ selectedSchedule.class_name }} ({{ selectedSchedule.class_code }})</v-list-item-subtitle>
              </v-list-item>
            </v-col>
            <v-col cols="12" md="6">
              <v-list-item>
                <v-list-item-title>
                  <v-icon class="mr-2">mdi-book-open-variant</v-icon>
                  Môn học
                </v-list-item-title>
                <v-list-item-subtitle>{{ selectedSchedule.subject_name }} ({{ selectedSchedule.subject_code }})</v-list-item-subtitle>
              </v-list-item>
            </v-col>
            <v-col cols="12" md="6">
              <v-list-item>
                <v-list-item-title>
                  <v-icon class="mr-2">mdi-book</v-icon>
                  Bài học
                </v-list-item-title>
                <v-list-item-subtitle>{{ selectedSchedule.lesson_title }}</v-list-item-subtitle>
              </v-list-item>
            </v-col>
            <v-col cols="12" md="6">
              <v-list-item>
                <v-list-item-title>
                  <v-icon class="mr-2">mdi-account-tie</v-icon>
                  Giảng viên
                </v-list-item-title>
                <v-list-item-subtitle>{{ selectedSchedule.instructor_name }}</v-list-item-subtitle>
              </v-list-item>
            </v-col>
            <v-col cols="12" md="6">
              <v-list-item>
                <v-list-item-title>
                  <v-icon class="mr-2">mdi-door</v-icon>
                  Phòng học
                </v-list-item-title>
                <v-list-item-subtitle>{{ selectedSchedule.room_name }} ({{ selectedSchedule.room_code }})</v-list-item-subtitle>
              </v-list-item>
            </v-col>
            <v-col cols="12" md="6">
              <v-list-item>
                <v-list-item-title>
                  <v-icon class="mr-2">mdi-format-list-bulleted-type</v-icon>
                  Loại bài học
                </v-list-item-title>
                <v-list-item-subtitle>
                  <v-chip :color="selectedSchedule.lesson_type === 'LT' ? 'blue' : 'green'" size="small">
                    {{ selectedSchedule.lesson_type === 'LT' ? 'Lý thuyết' : 'Thực hành' }}
                  </v-chip>
                </v-list-item-subtitle>
              </v-list-item>
            </v-col>
            <v-col cols="12" md="6">
              <v-list-item>
                <v-list-item-title>
                  <v-icon class="mr-2">mdi-timer</v-icon>
                  Thời lượng
                </v-list-item-title>
                <v-list-item-subtitle>{{ selectedSchedule.duration_hours }} giờ</v-list-item-subtitle>
              </v-list-item>
            </v-col>
            <v-col cols="12" md="6">
              <v-list-item>
                <v-list-item-title>
                  <v-icon class="mr-2">mdi-account</v-icon>
                  Người tạo
                </v-list-item-title>
                <v-list-item-subtitle>{{ selectedSchedule.created_by_name }}</v-list-item-subtitle>
              </v-list-item>
            </v-col>
            <v-col cols="12" v-if="selectedSchedule.practice_group_number">
              <v-list-item>
                <v-list-item-title>
                  <v-icon class="mr-2">mdi-account-group</v-icon>
                  Nhóm thực hành
                </v-list-item-title>
                <v-list-item-subtitle>Nhóm {{ selectedSchedule.practice_group_number }}</v-list-item-subtitle>
              </v-list-item>
            </v-col>
            <v-col cols="12" v-if="selectedSchedule.notes">
              <v-list-item>
                <v-list-item-title>
                  <v-icon class="mr-2">mdi-note-text</v-icon>
                  Ghi chú
                </v-list-item-title>
                <v-list-item-subtitle>{{ selectedSchedule.notes }}</v-list-item-subtitle>
              </v-list-item>
            </v-col>
          </v-row>
        </v-card-text>
        <v-divider></v-divider>
        <v-card-actions>
          <v-btn color="primary" prepend-icon="mdi-pencil" @click="editSchedule(selectedSchedule)">
            Chỉnh sửa
          </v-btn>
          <v-btn color="error" prepend-icon="mdi-delete" @click="deleteSchedule(selectedSchedule)">
            Xóa
          </v-btn>
          <v-spacer></v-spacer>
          <v-btn text @click="detailsDialog = false">Đóng</v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>

    <!-- Delete Confirmation Dialog -->
    <v-dialog v-model="deleteDialog" max-width="500px">
      <v-card>
        <v-card-title>Xác nhận xóa</v-card-title>
        <v-card-text>
          Bạn có chắc chắn muốn xóa lịch giảng dạy này không?
          <br><br>
          <strong>{{ scheduleToDelete?.class_name }} - {{ scheduleToDelete?.subject_name }}</strong>
          <br>
          <strong>{{ formatDate(scheduleToDelete?.schedule_date) }} - {{ scheduleToDelete?.time_slot_name }}</strong>
        </v-card-text>
        <v-card-actions>
          <v-spacer></v-spacer>
          <v-btn text @click="deleteDialog = false">Hủy</v-btn>
          <v-btn color="error" @click="confirmDelete" :loading="deleting">Xóa</v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { useRouter } from 'vue-router'
import {
  scheduleAPI,
  timeSlotAPI,
  academicYearAPI,
  semesterAPI,
  instructorAPI,
  classAPI,
  roomAPI
} from '../services/api'

const router = useRouter()

const loading = ref(false)
const deleting = ref(false)
const exporting = ref(false)
const exportingPdf = ref(false)

// Use string date for better reactivity
const getCurrentMondayString = () => {
  const today = new Date()
  const monday = new Date(today)
  monday.setDate(monday.getDate() - monday.getDay() + 1)
  return monday.toISOString().split('T')[0]
}

const currentWeekStart = ref(getCurrentMondayString())
const schedules = ref([])
const timeSlots = ref([])
const academicYears = ref([])
const semesters = ref([])
const allSemesters = ref([])
const instructors = ref([])
const classes = ref([])
const rooms = ref([])
const detailsDialog = ref(false)
const deleteDialog = ref(false)
const selectedSchedule = ref(null)
const scheduleToDelete = ref(null)

const filters = ref({
  academicYear: null,
  semester: null,
  instructor: null,
  class: null,
  room: null
})

// Computed property for filtered semesters based on academic year
const filteredSemesters = computed(() => {
  if (!filters.value.academicYear) {
    return allSemesters.value
  }
  return allSemesters.value.filter((s: any) => s.academic_year === filters.value.academicYear)
})

const weekDays = computed(() => {
  const days = []
  const dayNames = ['Thứ 2', 'Thứ 3', 'Thứ 4', 'Thứ 5', 'Thứ 6', 'Thứ 7', 'Chủ nhật']

  for (let i = 0; i < 7; i++) {
    const date = new Date(currentWeekStart.value + 'T00:00:00')
    date.setDate(date.getDate() + i)
    days.push({
      name: dayNames[i],
      date: date.toISOString().split('T')[0]
    })
  }
  return days
})

const weekTitle = computed(() => {
  const start = new Date(currentWeekStart.value + 'T00:00:00')
  const end = new Date(currentWeekStart.value + 'T00:00:00')
  end.setDate(end.getDate() + 6)

  return `${start.toLocaleDateString('vi-VN')} - ${end.toLocaleDateString('vi-VN')}`
})

onMounted(async () => {
  await loadInitialData()
  await loadWeeklySchedules()
})

const loadInitialData = async () => {
  try {
    const [timeSlotsRes, academicYearsRes, semestersRes, instructorsRes, classesRes, roomsRes] = await Promise.all([
      timeSlotAPI.list(),
      academicYearAPI.list(),
      semesterAPI.list(),
      instructorAPI.list(),
      classAPI.list(),
      roomAPI.list()
    ])

    timeSlots.value = timeSlotsRes.data.results || timeSlotsRes.data
    academicYears.value = academicYearsRes.data.results || academicYearsRes.data
    allSemesters.value = semestersRes.data.results || semestersRes.data
    instructors.value = instructorsRes.data.results || instructorsRes.data
    classes.value = classesRes.data.results || classesRes.data
    rooms.value = roomsRes.data.results || roomsRes.data

    // Set current academic year and semester as default
    const currentAcademicYear = academicYears.value.find((ay: any) => ay.is_current)
    if (currentAcademicYear) {
      filters.value.academicYear = currentAcademicYear.id
    }

    const currentSemester = allSemesters.value.find((s: any) => s.is_current)
    if (currentSemester) {
      filters.value.semester = currentSemester.id
    }
  } catch (error) {
    console.error('Error loading initial data:', error)
  }
}

// Method to handle academic year change
const onAcademicYearChange = () => {
  // Reset semester when academic year changes
  filters.value.semester = null
  loadWeeklySchedules()
}

const loadWeeklySchedules = async () => {
  loading.value = true
  try {
    const dateString = currentWeekStart.value // Already a string
    console.log('Loading weekly schedules for date:', dateString)

    const params = {
      date: dateString,
      ...Object.fromEntries(
        Object.entries(filters.value).filter(([_, value]) => value !== null && value !== '')
      )
    }

    const response = await scheduleAPI.weekly(params.date)
    let scheduleData = response.data.schedules || []

    // Apply client-side filters if needed
    if (filters.value.semester) {
      scheduleData = scheduleData.filter(s => s.semester === filters.value.semester)
    }
    if (filters.value.instructor) {
      scheduleData = scheduleData.filter(s => s.instructor === filters.value.instructor)
    }
    if (filters.value.class) {
      scheduleData = scheduleData.filter(s => s.class_obj === filters.value.class)
    }
    if (filters.value.room) {
      scheduleData = scheduleData.filter(s => s.room === filters.value.room)
    }

    schedules.value = scheduleData
  } catch (error) {
    console.error('Error loading weekly schedules:', error)
    schedules.value = []
  } finally {
    loading.value = false
  }
}

const getSchedulesForSlot = (date: string, timeSlotId: number) => {
  return schedules.value.filter(schedule =>
    schedule.schedule_date === date && schedule.time_slot === timeSlotId
  )
}

const getScheduleCountForDay = (date: string) => {
  return schedules.value.filter(schedule => schedule.schedule_date === date).length
}

const getScheduleClass = (schedule: any) => {
  return {
    'schedule-theory': schedule.lesson_type === 'LT',
    'schedule-practice': schedule.lesson_type === 'TH',
    'schedule-completed': schedule.status === 'COMPLETED',
    'schedule-cancelled': schedule.status === 'CANCELLED'
  }
}

const showScheduleDetails = (schedule: any) => {
  selectedSchedule.value = schedule
  detailsDialog.value = true
}

const editSchedule = (schedule: any) => {
  detailsDialog.value = false
  router.push(`/schedules/edit/${schedule.id}`)
}

const deleteSchedule = (schedule: any) => {
  scheduleToDelete.value = schedule
  detailsDialog.value = false
  deleteDialog.value = true
}

const confirmDelete = async () => {
  if (!scheduleToDelete.value) return

  deleting.value = true
  try {
    await scheduleAPI.delete(scheduleToDelete.value.id)
    await loadWeeklySchedules()
    deleteDialog.value = false
    scheduleToDelete.value = null
  } catch (error) {
    console.error('Error deleting schedule:', error)
  } finally {
    deleting.value = false
  }
}

const createScheduleForSlot = (date: string, timeSlotId: number) => {
  const timeSlot = timeSlots.value.find(ts => ts.id === timeSlotId)
  const queryParams = new URLSearchParams({
    date,
    time_slot: timeSlotId.toString(),
    ...(filters.value.semester && { semester: filters.value.semester.toString() })
  })

  router.push(`/schedules/create?${queryParams.toString()}`)
}

const previousWeek = async () => {
  console.log('Previous week clicked, current:', currentWeekStart.value)
  const currentDate = new Date(currentWeekStart.value + 'T00:00:00')
  currentDate.setDate(currentDate.getDate() - 7)
  const newDateString = currentDate.toISOString().split('T')[0]
  currentWeekStart.value = newDateString
  console.log('New week start:', currentWeekStart.value)
  await loadWeeklySchedules()
}

const nextWeek = async () => {
  console.log('Next week clicked, current:', currentWeekStart.value)
  const currentDate = new Date(currentWeekStart.value + 'T00:00:00')
  currentDate.setDate(currentDate.getDate() + 7)
  const newDateString = currentDate.toISOString().split('T')[0]
  currentWeekStart.value = newDateString
  console.log('New week start:', currentWeekStart.value)
  await loadWeeklySchedules()
}

const goToToday = () => {
  console.log('Go to today clicked')
  currentWeekStart.value = getCurrentMondayString()
  console.log('New week start:', currentWeekStart.value)
  loadWeeklySchedules()
}

const isToday = (date: string) => {
  const today = new Date().toISOString().split('T')[0]
  return date === today
}

const formatDate = (date: string) => {
  return new Date(date).toLocaleDateString('vi-VN')
}

const formatDisplayDate = (date: string) => {
  const dateObj = new Date(date)
  return `${dateObj.getDate()}/${dateObj.getMonth() + 1}`
}

const getSessionText = (session: string) => {
  switch (session) {
    case 'SANG': return 'Sáng'
    case 'CHIEU': return 'Chiều'
    case 'TOI': return 'Tối'
    default: return session
  }
}

const getStatusColor = (status: string) => {
  switch (status) {
    case 'SCHEDULED': return 'primary'
    case 'COMPLETED': return 'success'
    case 'CANCELLED': return 'error'
    default: return 'grey'
  }
}

const getStatusText = (status: string) => {
  switch (status) {
    case 'SCHEDULED': return 'Đã lên lịch'
    case 'COMPLETED': return 'Đã hoàn thành'
    case 'CANCELLED': return 'Đã hủy'
    default: return status
  }
}

// Export to Excel function
const exportToExcel = async () => {
  exporting.value = true
  try {
    // Get current filters
    const exportParams = {
      date: currentWeekStart.value,
      ...Object.fromEntries(
        Object.entries(filters.value).filter(([_, value]) => value !== null && value !== '')
      )
    }

    // Call backend API to generate Excel
    const response = await scheduleAPI.exportExcel(exportParams)

    // Create download link
    const url = window.URL.createObjectURL(new Blob([response.data]))
    const link = document.createElement('a')
    link.href = url

    // Generate filename with current date and filters
    const currentDate = new Date().toISOString().split('T')[0]
    const selectedSemester = allSemesters.value.find((s: any) => s.id === filters.value.semester)
    const semesterName = selectedSemester ? selectedSemester.name : 'TatCa'

    link.setAttribute('download', `LichGiangDay_${semesterName}_${currentDate}.xlsx`)
    document.body.appendChild(link)
    link.click()
    link.remove()
    window.URL.revokeObjectURL(url)

  } catch (error) {
    console.error('Error exporting to Excel:', error)
    // For now, show a message that feature is coming soon
    alert('Chức năng xuất Excel đang được phát triển. Vui lòng thử lại sau!')
  } finally {
    exporting.value = false
  }
}

// Export to Campus-wide Excel function
const exportCampusWideExcel = async () => {
  exporting.value = true
  try {
    // Get current filters
    const exportParams = {
      date: currentWeekStart.value,
      ...Object.fromEntries(
        Object.entries(filters.value).filter(([_, value]) => value !== null && value !== '')
      )
    }

    // Call backend API to generate Campus-wide Excel
    const response = await scheduleAPI.exportCampusWideExcel(exportParams)

    // Create download link
    const url = window.URL.createObjectURL(new Blob([response.data]))
    const link = document.createElement('a')
    link.href = url

    // Generate filename with current date and filters
    const currentDate = new Date().toISOString().split('T')[0]
    const selectedSemester = allSemesters.value.find((s: any) => s.id === filters.value.semester)
    const semesterName = selectedSemester ? selectedSemester.name : 'TatCa'

    link.setAttribute('download', `LichToanTruong_${semesterName}_${currentDate}.xlsx`)
    document.body.appendChild(link)
    link.click()
    link.remove()
    window.URL.revokeObjectURL(url)

  } catch (error) {
    console.error('Error exporting campus-wide Excel:', error)
    alert('Chức năng xuất mẫu toàn trường đang được phát triển. Vui lòng thử lại sau!')
  } finally {
    exporting.value = false
  }
}

// Export to Class Excel function
const exportClassExcel = async () => {
  exporting.value = true
  try {
    // Get current filters
    const exportParams = {
      date: currentWeekStart.value,
      ...Object.fromEntries(
        Object.entries(filters.value).filter(([_, value]) => value !== null && value !== '')
      )
    }

    // Call backend API to generate Class Excel
    const response = await scheduleAPI.exportClassExcel(exportParams)

    // Create download link
    const url = window.URL.createObjectURL(new Blob([response.data]))
    const link = document.createElement('a')
    link.href = url

    // Generate filename with current date and filters
    const currentDate = new Date().toISOString().split('T')[0]
    const selectedSemester = allSemesters.value.find((s: any) => s.id === filters.value.semester)
    const semesterName = selectedSemester ? selectedSemester.name : 'TatCa'
    const selectedClass = classes.value.find((c: any) => c.id === filters.value.class)
    const className = selectedClass ? selectedClass.code : 'TatCa'

    link.setAttribute('download', `LichTheoLop_${className}_${semesterName}_${currentDate}.xlsx`)
    document.body.appendChild(link)
    link.click()
    link.remove()
    window.URL.revokeObjectURL(url)

  } catch (error) {
    console.error('Error exporting class Excel:', error)
    alert('Chức năng xuất mẫu theo lớp đang được phát triển. Vui lòng thử lại sau!')
  } finally {
    exporting.value = false
  }
}

// Export to Room Statistics Excel function
const exportRoomStatisticsExcel = async () => {
  exporting.value = true
  try {
    // Get current filters
    const exportParams = {
      date: currentWeekStart.value,
      ...Object.fromEntries(
        Object.entries(filters.value).filter(([_, value]) => value !== null && value !== '')
      )
    }

    // Call backend API to generate Room Statistics Excel
    const response = await scheduleAPI.exportRoomStatisticsExcel(exportParams)

    // Create download link
    const url = window.URL.createObjectURL(new Blob([response.data]))
    const link = document.createElement('a')
    link.href = url

    // Generate filename with current date and filters
    const currentDate = new Date().toISOString().split('T')[0]
    const selectedSemester = allSemesters.value.find((s: any) => s.id === filters.value.semester)
    const semesterName = selectedSemester ? selectedSemester.name : 'TatCa'

    link.setAttribute('download', `ThongKePhong_${semesterName}_${currentDate}.xlsx`)
    document.body.appendChild(link)
    link.click()
    link.remove()
    window.URL.revokeObjectURL(url)

  } catch (error) {
    console.error('Error exporting room statistics Excel:', error)
    alert('Chức năng xuất thống kê phòng học đang được phát triển. Vui lòng thử lại sau!')
  } finally {
    exporting.value = false
  }
}

// Export to Campus-wide PDF function
const exportCampusWidePdf = async () => {
  exportingPdf.value = true
  try {
    // Get current filters
    const exportParams = {
      date: currentWeekStart.value,
      ...Object.fromEntries(
        Object.entries(filters.value).filter(([_, value]) => value !== null && value !== '')
      )
    }

    // Call backend API to generate Campus-wide PDF
    const response = await scheduleAPI.exportCampusWidePdf(exportParams)

    // Create download link
    const url = window.URL.createObjectURL(new Blob([response.data]))
    const link = document.createElement('a')
    link.href = url

    // Generate filename with current date and filters
    const currentDate = new Date().toISOString().split('T')[0]
    const selectedSemester = allSemesters.value.find((s: any) => s.id === filters.value.semester)
    const semesterName = selectedSemester ? selectedSemester.name : 'TatCa'

    link.setAttribute('download', `LichToanTruong_${semesterName}_${currentDate}.pdf`)
    document.body.appendChild(link)
    link.click()
    link.remove()
    window.URL.revokeObjectURL(url)

  } catch (error) {
    console.error('Error exporting campus-wide PDF:', error)
    alert('Chức năng xuất PDF toàn trường đang được phát triển. Vui lòng thử lại sau!')
  } finally {
    exportingPdf.value = false
  }
}

// Export to Class PDF function
const exportClassPdf = async () => {
  exportingPdf.value = true
  try {
    // Get current filters
    const exportParams = {
      date: currentWeekStart.value,
      ...Object.fromEntries(
        Object.entries(filters.value).filter(([_, value]) => value !== null && value !== '')
      )
    }

    // Call backend API to generate Class PDF
    const response = await scheduleAPI.exportClassPdf(exportParams)

    // Create download link
    const url = window.URL.createObjectURL(new Blob([response.data]))
    const link = document.createElement('a')
    link.href = url

    // Generate filename with current date and filters
    const currentDate = new Date().toISOString().split('T')[0]
    const selectedSemester = allSemesters.value.find((s: any) => s.id === filters.value.semester)
    const semesterName = selectedSemester ? selectedSemester.name : 'TatCa'
    const selectedClass = classes.value.find((c: any) => c.id === filters.value.class)
    const className = selectedClass ? selectedClass.code : 'TatCa'

    link.setAttribute('download', `LichTheoLop_${className}_${semesterName}_${currentDate}.pdf`)
    document.body.appendChild(link)
    link.click()
    link.remove()
    window.URL.revokeObjectURL(url)

  } catch (error) {
    console.error('Error exporting class PDF:', error)
    alert('Chức năng xuất PDF theo lớp đang được phát triển. Vui lòng thử lại sau!')
  } finally {
    exportingPdf.value = false
  }
}

// Export to Room Statistics PDF function
const exportRoomStatisticsPdf = async () => {
  exportingPdf.value = true
  try {
    // Get current filters
    const exportParams = {
      date: currentWeekStart.value,
      ...Object.fromEntries(
        Object.entries(filters.value).filter(([_, value]) => value !== null && value !== '')
      )
    }

    // Call backend API to generate Room Statistics PDF
    const response = await scheduleAPI.exportRoomStatisticsPdf(exportParams)

    // Create download link
    const url = window.URL.createObjectURL(new Blob([response.data]))
    const link = document.createElement('a')
    link.href = url

    // Generate filename with current date and filters
    const currentDate = new Date().toISOString().split('T')[0]
    const selectedSemester = allSemesters.value.find((s: any) => s.id === filters.value.semester)
    const semesterName = selectedSemester ? selectedSemester.name : 'TatCa'

    link.setAttribute('download', `ThongKePhong_${semesterName}_${currentDate}.pdf`)
    document.body.appendChild(link)
    link.click()
    link.remove()
    window.URL.revokeObjectURL(url)

  } catch (error) {
    console.error('Error exporting room statistics PDF:', error)
    alert('Chức năng xuất PDF thống kê phòng học đang được phát triển. Vui lòng thử lại sau!')
  } finally {
    exportingPdf.value = false
  }
}

// Export to PDF function
const exportToPdf = async () => {
  exportingPdf.value = true
  try {
    // Get current filters
    const exportParams = {
      date: currentWeekStart.value,
      ...Object.fromEntries(
        Object.entries(filters.value).filter(([_, value]) => value !== null && value !== '')
      )
    }

    // Call backend API to generate PDF
    const response = await scheduleAPI.exportPdf(exportParams)

    // Create download link
    const url = window.URL.createObjectURL(new Blob([response.data]))
    const link = document.createElement('a')
    link.href = url

    // Generate filename with current date and filters
    const currentDate = new Date().toISOString().split('T')[0]
    const selectedSemester = allSemesters.value.find((s: any) => s.id === filters.value.semester)
    const semesterName = selectedSemester ? selectedSemester.name : 'TatCa'

    link.setAttribute('download', `LichGiangDay_${semesterName}_${currentDate}.pdf`)
    document.body.appendChild(link)
    link.click()
    link.remove()
    window.URL.revokeObjectURL(url)

  } catch (error) {
    console.error('Error exporting to PDF:', error)
    alert('Chức năng xuất PDF đang được phát triển. Vui lòng thử lại sau!')
  } finally {
    exportingPdf.value = false
  }
}
</script>

<style scoped>
.calendar-container {
  overflow-x: auto;
  min-height: 600px;
}

.calendar-grid {
  display: grid;
  grid-template-columns: 140px repeat(7, minmax(150px, 1fr));
  gap: 1px;
  background-color: #e0e0e0;
  min-width: 1200px;
}

.time-header,
.day-header,
.time-slot-label,
.calendar-cell {
  background-color: white;
  padding: 8px;
  min-height: 100px;
  position: relative;
}

.time-header {
  background-color: #f5f5f5;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
}

.day-header {
  text-align: center;
  font-weight: bold;
  background-color: #f5f5f5;
  display: flex;
  flex-direction: column;
  justify-content: center;
  transition: background-color 0.2s;
}

.day-header.today {
  background-color: #e3f2fd;
  border: 2px solid #2196f3;
}

.day-name {
  font-size: 14px;
  font-weight: bold;
  margin-bottom: 4px;
}

.day-date {
  font-size: 16px;
  color: #666;
}

.day-count {
  font-size: 11px;
  color: #999;
  margin-top: 4px;
}

.time-slot-label {
  background-color: #f5f5f5;
  font-size: 12px;
  text-align: center;
  display: flex;
  flex-direction: column;
  justify-content: center;
  border-right: 2px solid #e0e0e0;
}

.slot-name {
  font-weight: bold;
  margin-bottom: 4px;
}

.slot-time {
  color: #666;
  font-size: 10px;
}

.slot-session {
  color: #999;
  font-size: 9px;
  margin-top: 2px;
}

.calendar-cell {
  cursor: pointer;
  transition: background-color 0.2s;
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.calendar-cell:hover {
  background-color: #f8f9fa;
}

.calendar-cell.today {
  background-color: #f3e5f5;
}

.schedule-item {
  background: linear-gradient(135deg, #2196f3 0%, #1976d2 100%);
  color: white;
  padding: 6px;
  border-radius: 6px;
  font-size: 10px;
  cursor: pointer;
  transition: all 0.2s;
  position: relative;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.schedule-item:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0,0,0,0.15);
}

.schedule-theory {
  background: linear-gradient(135deg, #2196f3 0%, #1976d2 100%);
}

.schedule-practice {
  background: linear-gradient(135deg, #4caf50 0%, #388e3c 100%);
}

.schedule-completed {
  background: linear-gradient(135deg, #66bb6a 0%, #4caf50 100%);
}

.schedule-cancelled {
  background: linear-gradient(135deg, #f44336 0%, #d32f2f 100%);
  opacity: 0.7;
}

.schedule-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 4px;
}

.schedule-class {
  font-weight: bold;
  font-size: 11px;
  margin-bottom: 2px;
}

.schedule-subject {
  font-size: 10px;
  opacity: 0.9;
  margin-bottom: 2px;
}

.schedule-room,
.schedule-instructor {
  font-size: 9px;
  opacity: 0.8;
  display: flex;
  align-items: center;
  gap: 2px;
  margin-bottom: 1px;
}

.schedule-actions {
  display: flex;
  justify-content: flex-end;
  gap: 2px;
  margin-top: 4px;
  opacity: 0;
  transition: opacity 0.2s;
}

.schedule-item:hover .schedule-actions {
  opacity: 1;
}

.empty-slot {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: #ccc;
  font-size: 11px;
  opacity: 0;
  transition: opacity 0.2s;
}

.calendar-cell:hover .empty-slot {
  opacity: 1;
}

.empty-text {
  margin-top: 4px;
}

/* Responsive */
@media (max-width: 1200px) {
  .calendar-grid {
    grid-template-columns: 120px repeat(7, minmax(120px, 1fr));
  }

  .schedule-item {
    font-size: 9px;
    padding: 4px;
  }
}

@media (max-width: 768px) {
  .calendar-grid {
    grid-template-columns: 100px repeat(7, minmax(100px, 1fr));
  }

  .time-slot-label,
  .calendar-cell {
    min-height: 80px;
    padding: 4px;
  }

  .schedule-item {
    font-size: 8px;
    padding: 3px;
  }
}
</style>
