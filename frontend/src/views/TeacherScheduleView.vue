<template>
  <v-container fluid>
    <!-- No Instructor Profile Alert -->
    <v-alert
      v-if="!instructorInfo && !loadingInstructor && hasTriedLoadingInstructor"
      type="warning"
      variant="tonal"
      class="mb-6"
      prominent
    >
      <template v-slot:prepend>
        <v-icon size="32">mdi-account-alert</v-icon>
      </template>

      <v-alert-title class="text-h6 mb-2">
        Chưa có hồ sơ giảng viên
      </v-alert-title>

      <div class="mb-4">
        Bạn chưa có hồ sơ giảng viên trong hệ thống. <PERSON><PERSON> xem lịch giảng dạy, bạn cần có hồ sơ giảng viên.
      </div>

      <div class="d-flex flex-wrap gap-3">
        <v-btn
          color="primary"
          variant="elevated"
          prepend-icon="mdi-account-plus"
          @click="showCreateProfileDialog = true"
        >
          Tạo hồ sơ giảng viên
        </v-btn>

        <v-btn
          color="info"
          variant="outlined"
          prepend-icon="mdi-refresh"
          @click="loadInstructorInfo"
          :loading="loadingInstructor"
        >
          Kiểm tra lại
        </v-btn>

        <v-btn
          color="warning"
          variant="text"
          prepend-icon="mdi-help-circle"
          @click="showHelpDialog = true"
        >
          Cần hỗ trợ?
        </v-btn>
      </div>
    </v-alert>

    <!-- Header -->
    <v-row class="mb-4" v-if="instructorInfo">
      <v-col>
        <div class="d-flex justify-space-between align-center">
          <h2>
            <v-icon class="mr-2">mdi-calendar-account</v-icon>
            Lịch giảng dạy của tôi
          </h2>
          <div class="d-flex align-center">
            <v-chip color="primary" class="mr-2">
              <v-icon start>mdi-account-tie</v-icon>
              {{ instructorInfo.full_name }}
            </v-chip>
            <v-chip color="info">
              <v-icon start>mdi-card-account-details</v-icon>
              {{ instructorInfo.employee_code }}
            </v-chip>
          </div>
        </div>
      </v-col>
    </v-row>

    <!-- Filters -->
    <v-card class="mb-4" v-if="instructorInfo">
      <v-card-text>
        <v-row>
          <v-col cols="12" md="3">
            <v-text-field
              v-model="selectedDate"
              label="Tuần học"
              type="date"
              @update:model-value="loadSchedules"
              hint="Chọn ngày trong tuần để xem lịch cả tuần"
            ></v-text-field>
          </v-col>
          <v-col cols="12" md="3">
            <v-select
              v-model="semesterFilter"
              label="Học kỳ"
              :items="semesterOptions"
              @update:model-value="loadSchedules"
            ></v-select>
          </v-col>
          <v-col cols="12" md="3">
            <v-select
              v-model="statusFilter"
              label="Trạng thái"
              :items="statusOptions"
              @update:model-value="loadSchedules"
            ></v-select>
          </v-col>
          <v-col cols="12" md="3" class="d-flex align-end">
            <v-btn
              variant="outlined"
              @click="resetFilters"
              prepend-icon="mdi-refresh"
              class="mr-2"
            >
              Đặt lại
            </v-btn>
            <v-btn
              color="success"
              @click="exportExcel"
              prepend-icon="mdi-file-excel"
              :loading="exporting"
            >
              Xuất Excel
            </v-btn>
          </v-col>
        </v-row>
      </v-card-text>
    </v-card>

    <!-- Week Navigation -->
    <v-card class="mb-4" v-if="instructorInfo">
      <v-card-text>
        <div class="d-flex justify-space-between align-center">
          <v-btn
            icon="mdi-chevron-left"
            @click="previousWeek"
            variant="text"
          ></v-btn>
          <div class="text-center">
            <h3>{{ weekDisplayText }}</h3>
            <p class="text-caption">{{ weekDateRange }}</p>
          </div>
          <v-btn
            icon="mdi-chevron-right"
            @click="nextWeek"
            variant="text"
          ></v-btn>
        </div>
      </v-card-text>
    </v-card>

    <!-- Loading -->
    <v-row v-if="loading" class="justify-center">
      <v-col cols="auto">
        <v-progress-circular
          indeterminate
          color="primary"
          size="64"
        ></v-progress-circular>
      </v-col>
    </v-row>

    <!-- Weekly Schedule Grid -->
    <v-card v-else-if="instructorInfo">
      <v-card-title>
        <v-icon class="mr-2">mdi-calendar-week</v-icon>
        Lịch tuần ({{ schedules.length }} tiết học)
      </v-card-title>
      
      <v-card-text>
        <!-- Time slots header -->
        <v-row class="mb-2">
          <v-col cols="2" class="text-center font-weight-bold">
            Thời gian
          </v-col>
          <v-col 
            v-for="day in weekDays" 
            :key="day.date"
            cols="2" 
            class="text-center font-weight-bold"
          >
            {{ day.name }}
            <br>
            <small>{{ formatDate(day.date) }}</small>
          </v-col>
        </v-row>

        <v-divider class="mb-4"></v-divider>

        <!-- Schedule grid -->
        <div v-for="timeSlot in timeSlots" :key="timeSlot.id" class="mb-3">
          <v-row>
            <!-- Time slot info -->
            <v-col cols="2" class="d-flex align-center">
              <v-card variant="outlined" class="pa-2 w-100">
                <div class="text-center">
                  <div class="font-weight-bold">{{ timeSlot.slot_name }}</div>
                  <div class="text-caption">{{ formatTime(timeSlot.start_time) }} - {{ formatTime(timeSlot.end_time) }}</div>
                  <v-chip 
                    :color="getSessionColor(timeSlot.session)"
                    size="small"
                    class="mt-1"
                  >
                    {{ getSessionName(timeSlot.session) }}
                  </v-chip>
                </div>
              </v-card>
            </v-col>

            <!-- Schedule for each day -->
            <v-col 
              v-for="day in weekDays" 
              :key="`${timeSlot.id}-${day.date}`"
              cols="2"
            >
              <v-card 
                v-if="getScheduleForDay(day.date, timeSlot.id)"
                :color="getScheduleColor(getScheduleForDay(day.date, timeSlot.id))"
                class="pa-2 h-100"
                variant="tonal"
              >
                <div class="text-caption">
                  <div class="font-weight-bold">{{ getScheduleForDay(day.date, timeSlot.id).subject_name }}</div>
                  <div>Lớp: {{ getScheduleForDay(day.date, timeSlot.id).class_code }}</div>
                  <div>Phòng: {{ getScheduleForDay(day.date, timeSlot.id).room_code }}</div>
                  <div v-if="getScheduleForDay(day.date, timeSlot.id).lesson_title">
                    Bài: {{ getScheduleForDay(day.date, timeSlot.id).lesson_title }}
                  </div>
                  <v-chip 
                    :color="getStatusColor(getScheduleForDay(day.date, timeSlot.id).status)"
                    size="x-small"
                    class="mt-1"
                  >
                    {{ getStatusName(getScheduleForDay(day.date, timeSlot.id).status) }}
                  </v-chip>
                </div>
              </v-card>
              <v-card 
                v-else
                variant="outlined"
                class="pa-2 h-100 d-flex align-center justify-center"
                style="min-height: 120px;"
              >
                <div class="text-center text-grey">
                  <v-icon size="24" color="grey-lighten-1">mdi-calendar-blank</v-icon>
                  <div class="text-caption">Trống</div>
                </div>
              </v-card>
            </v-col>
          </v-row>
        </div>

        <!-- No schedules message -->
        <div v-if="schedules.length === 0" class="text-center pa-8">
          <v-icon size="64" color="grey">mdi-calendar-remove</v-icon>
          <div class="text-h6 mt-2">Không có lịch giảng dạy</div>
          <div class="text-caption">Tuần này bạn không có lịch giảng dạy nào</div>
        </div>
      </v-card-text>
    </v-card>

    <!-- Statistics -->
    <v-row class="mt-4" v-if="instructorInfo">
      <v-col cols="12" md="3">
        <v-card color="primary" dark>
          <v-card-text>
            <div class="d-flex align-center">
              <v-icon size="40" class="mr-3">mdi-calendar-check</v-icon>
              <div>
                <div class="text-h6">{{ schedules.length }}</div>
                <div class="text-caption">Tiết học tuần này</div>
              </div>
            </div>
          </v-card-text>
        </v-card>
      </v-col>

      <v-col cols="12" md="3">
        <v-card color="success" dark>
          <v-card-text>
            <div class="d-flex align-center">
              <v-icon size="40" class="mr-3">mdi-clock-check</v-icon>
              <div>
                <div class="text-h6">{{ totalHours }}</div>
                <div class="text-caption">Tổng giờ giảng</div>
              </div>
            </div>
          </v-card-text>
        </v-card>
      </v-col>

      <v-col cols="12" md="3">
        <v-card color="info" dark>
          <v-card-text>
            <div class="d-flex align-center">
              <v-icon size="40" class="mr-3">mdi-book-open-page-variant</v-icon>
              <div>
                <div class="text-h6">{{ uniqueSubjects.length }}</div>
                <div class="text-caption">Môn học</div>
              </div>
            </div>
          </v-card-text>
        </v-card>
      </v-col>

      <v-col cols="12" md="3">
        <v-card color="warning" dark>
          <v-card-text>
            <div class="d-flex align-center">
              <v-icon size="40" class="mr-3">mdi-google-classroom</v-icon>
              <div>
                <div class="text-h6">{{ uniqueClasses.length }}</div>
                <div class="text-caption">Lớp học</div>
              </div>
            </div>
          </v-card-text>
        </v-card>
      </v-col>
    </v-row>

    <!-- Create Profile Dialog -->
    <CreateInstructorProfileDialog
      v-model="showCreateProfileDialog"
      @success="onProfileCreated"
    />

    <!-- Help Dialog -->
    <v-dialog v-model="showHelpDialog" max-width="600px">
      <v-card>
        <v-card-title class="text-h5 bg-info text-white">
          <v-icon start>mdi-help-circle</v-icon>
          Hướng dẫn tạo hồ sơ giảng viên
        </v-card-title>

        <v-card-text class="pa-6">
          <div class="mb-4">
            <h3 class="text-h6 mb-2">Tại sao cần hồ sơ giảng viên?</h3>
            <p>Hồ sơ giảng viên là thông tin cần thiết để hệ thống có thể:</p>
            <ul class="ml-4">
              <li>Hiển thị lịch giảng dạy của bạn</li>
              <li>Quản lý thông tin cá nhân và liên hệ</li>
              <li>Theo dõi giờ giảng và khối lượng công việc</li>
              <li>Tạo báo cáo và thống kê</li>
            </ul>
          </div>

          <div class="mb-4">
            <h3 class="text-h6 mb-2">Cách tạo hồ sơ:</h3>
            <v-stepper alt-labels>
              <v-stepper-header>
                <v-stepper-item title="Bước 1" value="1">Điền thông tin</v-stepper-item>
                <v-divider></v-divider>
                <v-stepper-item title="Bước 2" value="2">Chọn khoa</v-stepper-item>
                <v-divider></v-divider>
                <v-stepper-item title="Bước 3" value="3">Hoàn thành</v-stepper-item>
              </v-stepper-header>
            </v-stepper>
          </div>

          <v-alert type="info" variant="tonal">
            <strong>Lưu ý:</strong> Sau khi tạo hồ sơ, quản trị viên sẽ xem xét và kích hoạt tài khoản của bạn.
          </v-alert>
        </v-card-text>

        <v-card-actions class="pa-6 pt-0">
          <v-spacer></v-spacer>
          <v-btn variant="outlined" @click="showHelpDialog = false">
            Đóng
          </v-btn>
          <v-btn color="primary" @click="showHelpDialog = false; showCreateProfileDialog = true">
            Tạo hồ sơ ngay
          </v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>
  </v-container>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { scheduleAPI, semesterAPI, instructorAPI } from '@/services/api'
import { useAuthStore } from '@/stores/auth'
import { toast } from '@/composables/useToast'
import CreateInstructorProfileDialog from '@/components/CreateInstructorProfileDialog.vue'

// Auth
const authStore = useAuthStore()

// Reactive data
const schedules = ref([])
const semesters = ref([])
const timeSlots = ref([])
const instructorInfo = ref(null)
const loading = ref(false)
const loadingInstructor = ref(false)
const hasTriedLoadingInstructor = ref(false)
const exporting = ref(false)
const showCreateProfileDialog = ref(false)
const showHelpDialog = ref(false)

// Filters
const selectedDate = ref(new Date().toISOString().split('T')[0])
const semesterFilter = ref('')
const statusFilter = ref('')

// Options
const statusOptions = [
  { title: 'Tất cả', value: '' },
  { title: 'Đã lên lịch', value: 'SCHEDULED' },
  { title: 'Đã hoàn thành', value: 'COMPLETED' },
  { title: 'Đã hủy', value: 'CANCELLED' }
]

const semesterOptions = computed(() => [
  { title: 'Tất cả học kỳ', value: '' },
  ...semesters.value.map(semester => ({
    title: `${semester.name} (${semester.academic_year_code})`,
    value: semester.id
  }))
])

// Week calculation
const weekStart = computed(() => {
  const date = new Date(selectedDate.value)
  const day = date.getDay()
  const diff = date.getDate() - day + (day === 0 ? -6 : 1) // Monday
  return new Date(date.setDate(diff))
})

const weekDays = computed(() => {
  const days = []
  const dayNames = ['Thứ 2', 'Thứ 3', 'Thứ 4', 'Thứ 5', 'Thứ 6', 'Thứ 7', 'Chủ nhật']
  
  for (let i = 0; i < 7; i++) {
    const date = new Date(weekStart.value)
    date.setDate(weekStart.value.getDate() + i)
    days.push({
      name: dayNames[i],
      date: date.toISOString().split('T')[0]
    })
  }
  return days
})

const weekDisplayText = computed(() => {
  const weekNum = Math.ceil((weekStart.value.getDate() - 1) / 7) + 1
  return `Tuần ${weekNum}`
})

const weekDateRange = computed(() => {
  const start = weekStart.value.toLocaleDateString('vi-VN')
  const end = new Date(weekStart.value)
  end.setDate(weekStart.value.getDate() + 6)
  return `${start} - ${end.toLocaleDateString('vi-VN')}`
})

// Statistics
const totalHours = computed(() => {
  return schedules.value.reduce((total, schedule) => {
    return total + (schedule.time_slot_duration || 1.5)
  }, 0)
})

const uniqueSubjects = computed(() => {
  const subjects = new Set(schedules.value.map(s => s.subject_id))
  return Array.from(subjects)
})

const uniqueClasses = computed(() => {
  const classes = new Set(schedules.value.map(s => s.class_id))
  return Array.from(classes)
})

// Methods
const loadInstructorInfo = async () => {
  try {
    loadingInstructor.value = true
    hasTriedLoadingInstructor.value = true
    const response = await instructorAPI.myProfile()
    instructorInfo.value = response.data

    // If we have instructor info, load schedules
    if (instructorInfo.value) {
      await loadSchedules()
    }
  } catch (error: any) {
    console.error('Error loading instructor info:', error)
    // If no instructor profile found, don't show alert anymore
    // The UI will handle this with the alert component
    if (error.response?.status === 404) {
      instructorInfo.value = null
    } else {
      toast.error('Lỗi khi tải thông tin giảng viên')
    }
  } finally {
    loadingInstructor.value = false
  }
}

const loadSemesters = async () => {
  try {
    const response = await semesterAPI.list()
    semesters.value = response.data.results || response.data
  } catch (error) {
    console.error('Error loading semesters:', error)
  }
}

const loadSchedules = async () => {
  // Don't load schedules if no instructor info
  if (!instructorInfo.value) {
    return
  }

  try {
    loading.value = true
    const params = {
      date: selectedDate.value
    }

    if (semesterFilter.value) {
      params.semester = semesterFilter.value
    }

    if (statusFilter.value) {
      params.status = statusFilter.value
    }

    const response = await scheduleAPI.weekly(params)
    schedules.value = response.data.schedules || []

    // Extract unique time slots
    const timeSlotSet = new Set()
    schedules.value.forEach(schedule => {
      if (schedule.time_slot_id) {
        timeSlotSet.add(JSON.stringify({
          id: schedule.time_slot_id,
          slot_name: schedule.time_slot_name,
          start_time: schedule.time_slot_start_time,
          end_time: schedule.time_slot_end_time,
          session: schedule.time_slot_session
        }))
      }
    })

    timeSlots.value = Array.from(timeSlotSet).map(ts => JSON.parse(ts))
    timeSlots.value.sort((a, b) => a.start_time.localeCompare(b.start_time))

  } catch (error) {
    console.error('Error loading schedules:', error)
    toast.error('Lỗi khi tải lịch giảng dạy')
  } finally {
    loading.value = false
  }
}

const resetFilters = () => {
  selectedDate.value = new Date().toISOString().split('T')[0]
  semesterFilter.value = ''
  statusFilter.value = ''
  loadSchedules()
}

const previousWeek = () => {
  const date = new Date(selectedDate.value)
  date.setDate(date.getDate() - 7)
  selectedDate.value = date.toISOString().split('T')[0]
  loadSchedules()
}

const nextWeek = () => {
  const date = new Date(selectedDate.value)
  date.setDate(date.getDate() + 7)
  selectedDate.value = date.toISOString().split('T')[0]
  loadSchedules()
}

const onProfileCreated = () => {
  toast.success('Tạo hồ sơ giảng viên thành công!')
  // Reload instructor info
  loadInstructorInfo()
}

const exportExcel = async () => {
  try {
    exporting.value = true
    const params = {
      date: selectedDate.value,
      instructor: instructorInfo.value?.id
    }

    if (semesterFilter.value) {
      params.semester = semesterFilter.value
    }

    const response = await scheduleAPI.exportExcel(params)

    // Create download link
    const url = window.URL.createObjectURL(new Blob([response.data]))
    const link = document.createElement('a')
    link.href = url
    link.setAttribute('download', `LichGiangDay_${instructorInfo.value?.full_name}_${selectedDate.value}.xlsx`)
    document.body.appendChild(link)
    link.click()
    link.remove()
    window.URL.revokeObjectURL(url)

  } catch (error) {
    console.error('Error exporting Excel:', error)
    toast.error('Lỗi khi xuất file Excel')
  } finally {
    exporting.value = false
  }
}

// Utility functions
const getScheduleForDay = (date: string, timeSlotId: number) => {
  return schedules.value.find(s => 
    s.schedule_date === date && s.time_slot_id === timeSlotId
  )
}

const formatDate = (dateString: string) => {
  const date = new Date(dateString)
  return `${date.getDate()}/${date.getMonth() + 1}`
}

const formatTime = (timeString: string) => {
  if (!timeString) return ''
  return timeString.substring(0, 5) // HH:MM
}

const getSessionName = (session: string) => {
  const sessionMap = {
    'SANG': 'Sáng',
    'CHIEU': 'Chiều',
    'TOI': 'Tối'
  }
  return sessionMap[session] || session
}

const getSessionColor = (session: string) => {
  const colorMap = {
    'SANG': 'orange',
    'CHIEU': 'blue',
    'TOI': 'purple'
  }
  return colorMap[session] || 'grey'
}

const getScheduleColor = (schedule: any) => {
  if (!schedule) return 'grey'
  
  const colorMap = {
    'SCHEDULED': 'primary',
    'COMPLETED': 'success',
    'CANCELLED': 'error'
  }
  return colorMap[schedule.status] || 'info'
}

const getStatusName = (status: string) => {
  const statusMap = {
    'SCHEDULED': 'Đã lên lịch',
    'COMPLETED': 'Hoàn thành',
    'CANCELLED': 'Đã hủy'
  }
  return statusMap[status] || status
}

const getStatusColor = (status: string) => {
  const colorMap = {
    'SCHEDULED': 'primary',
    'COMPLETED': 'success',
    'CANCELLED': 'error'
  }
  return colorMap[status] || 'grey'
}

// Lifecycle
onMounted(() => {
  loadInstructorInfo()
  loadSemesters()
  // loadSchedules() will be called from loadInstructorInfo if instructor exists
})
</script>

<style scoped>
.text-primary {
  color: rgb(var(--v-theme-primary)) !important;
}
</style>
