<template>
  <div>
    <v-row>
      <v-col cols="12">
        <h1 class="text-h4 mb-4">Quản lý giảng viên</h1>
      </v-col>
    </v-row>

    <!-- Filters -->
    <v-card class="mb-4">
      <v-card-title>Bộ lọc</v-card-title>
      <v-card-text>
        <v-row>
          <v-col cols="12" md="4">
            <v-select
              v-model="filters.department"
              :items="departments"
              item-title="name"
              item-value="id"
              label="Khoa"
              clearable
              @update:model-value="loadInstructors"
            ></v-select>
          </v-col>
          <v-col cols="12" md="4">
            <v-select
              v-model="filters.is_active"
              :items="statusOptions"
              item-title="text"
              item-value="value"
              label="Trạng thái"
              clearable
              @update:model-value="loadInstructors"
            ></v-select>
          </v-col>
          <v-col cols="12" md="4">
            <v-text-field
              v-model="search"
              prepend-icon="mdi-magnify"
              label="Tìm kiếm..."
              single-line
              hide-details
              clearable
            ></v-text-field>
          </v-col>
        </v-row>
      </v-card-text>
    </v-card>

    <!-- Data Table -->
    <v-card>
      <v-card-title class="d-flex align-center">
        <v-icon class="mr-2">mdi-account-tie</v-icon>
        Danh sách giảng viên
        <v-spacer></v-spacer>
        <v-btn color="primary" prepend-icon="mdi-plus" @click="openCreateDialog">
          Thêm giảng viên
        </v-btn>
        <v-btn color="info" prepend-icon="mdi-refresh" class="ml-2" @click="loadInstructors">
          Làm mới
        </v-btn>
      </v-card-title>

      <v-data-table
        :headers="headers"
        :items="instructors"
        :search="search"
        :loading="loading"
        class="elevation-1"
        :items-per-page="15"
      >
        <template v-slot:item.user_username="{ item }">
          <div v-if="item.user_username">
            <v-chip color="success" size="small" prepend-icon="mdi-account-check">
              {{ item.user_username }}
            </v-chip>
          </div>
          <div v-else>
            <v-chip color="warning" size="small" prepend-icon="mdi-account-alert">
              Chưa liên kết
            </v-chip>
          </div>
        </template>

        <template v-slot:item.is_active="{ item }">
          <v-chip
            :color="item.is_active ? 'success' : 'error'"
            size="small"
          >
            {{ item.is_active ? 'Hoạt động' : 'Không hoạt động' }}
          </v-chip>
        </template>

        <template v-slot:item.actions="{ item }">
          <v-btn icon="mdi-eye" size="small" @click="viewInstructor(item)" class="mr-1"></v-btn>
          <v-btn icon="mdi-pencil" size="small" @click="editInstructor(item)" class="mr-1"></v-btn>
          <v-btn
            v-if="!item.user_username"
            icon="mdi-account-plus"
            size="small"
            color="info"
            @click="linkUser(item)"
            class="mr-1"
            title="Liên kết tài khoản"
          ></v-btn>
          <v-btn icon="mdi-delete" size="small" color="error" @click="deleteInstructor(item)"></v-btn>
        </template>
      </v-data-table>
    </v-card>

    <!-- Create/Edit Dialog -->
    <v-dialog v-model="dialog" max-width="600px" persistent>
      <v-card>
        <v-card-title>
          <span class="text-h5">{{ editingItem ? 'Chỉnh sửa giảng viên' : 'Thêm giảng viên mới' }}</span>
        </v-card-title>
        <v-card-text>
          <v-form ref="form" v-model="valid">
            <v-row>
              <v-col cols="12" md="6">
                <v-text-field
                  v-model="formData.employee_code"
                  label="Mã giảng viên *"
                  :rules="[rules.required]"
                  required
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="6">
                <v-text-field
                  v-model="formData.full_name"
                  label="Họ và tên *"
                  :rules="[rules.required]"
                  required
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="6">
                <v-text-field
                  v-model="formData.email"
                  label="Email"
                  :rules="[rules.email]"
                  type="email"
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="6">
                <v-text-field
                  v-model="formData.phone"
                  label="Số điện thoại"
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="6">
                <v-select
                  v-model="formData.department"
                  :items="departments"
                  item-title="name"
                  item-value="id"
                  label="Khoa *"
                  :rules="[rules.required]"
                  required
                ></v-select>
              </v-col>
              <v-col cols="12" md="6">
                <v-text-field
                  v-model="formData.max_teaching_hours"
                  label="Số giờ dạy tối đa"
                  type="number"
                  :rules="[rules.required]"
                  required
                ></v-text-field>
              </v-col>
              <v-col cols="12">
                <v-switch
                  v-model="formData.is_active"
                  label="Hoạt động"
                  color="primary"
                ></v-switch>
              </v-col>
            </v-row>
          </v-form>
        </v-card-text>
        <v-card-actions>
          <v-spacer></v-spacer>
          <v-btn text @click="closeDialog">Hủy</v-btn>
          <v-btn color="primary" @click="saveInstructor" :loading="saving" :disabled="!valid">
            {{ editingItem ? 'Cập nhật' : 'Thêm' }}
          </v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>

    <!-- View Dialog -->
    <v-dialog v-model="viewDialog" max-width="600px">
      <v-card v-if="viewingItem">
        <v-card-title>
          <v-icon class="mr-2">mdi-account-tie</v-icon>
          Thông tin giảng viên
        </v-card-title>
        <v-card-text>
          <v-row>
            <v-col cols="12" md="6">
              <v-list-item>
                <v-list-item-title>Mã giảng viên</v-list-item-title>
                <v-list-item-subtitle>{{ viewingItem.employee_code }}</v-list-item-subtitle>
              </v-list-item>
            </v-col>
            <v-col cols="12" md="6">
              <v-list-item>
                <v-list-item-title>Họ và tên</v-list-item-title>
                <v-list-item-subtitle>{{ viewingItem.full_name }}</v-list-item-subtitle>
              </v-list-item>
            </v-col>
            <v-col cols="12" md="6">
              <v-list-item>
                <v-list-item-title>Email</v-list-item-title>
                <v-list-item-subtitle>{{ viewingItem.email || 'Chưa có' }}</v-list-item-subtitle>
              </v-list-item>
            </v-col>
            <v-col cols="12" md="6">
              <v-list-item>
                <v-list-item-title>Số điện thoại</v-list-item-title>
                <v-list-item-subtitle>{{ viewingItem.phone || 'Chưa có' }}</v-list-item-subtitle>
              </v-list-item>
            </v-col>
            <v-col cols="12" md="6">
              <v-list-item>
                <v-list-item-title>Khoa</v-list-item-title>
                <v-list-item-subtitle>{{ viewingItem.department_name }}</v-list-item-subtitle>
              </v-list-item>
            </v-col>
            <v-col cols="12" md="6">
              <v-list-item>
                <v-list-item-title>Số giờ dạy tối đa</v-list-item-title>
                <v-list-item-subtitle>{{ viewingItem.max_teaching_hours }} giờ</v-list-item-subtitle>
              </v-list-item>
            </v-col>
            <v-col cols="12">
              <v-list-item>
                <v-list-item-title>Trạng thái</v-list-item-title>
                <v-list-item-subtitle>
                  <v-chip :color="viewingItem.is_active ? 'success' : 'error'" size="small">
                    {{ viewingItem.is_active ? 'Hoạt động' : 'Không hoạt động' }}
                  </v-chip>
                </v-list-item-subtitle>
              </v-list-item>
            </v-col>
          </v-row>
        </v-card-text>
        <v-card-actions>
          <v-spacer></v-spacer>
          <v-btn color="primary" prepend-icon="mdi-pencil" @click="editFromView">
            Chỉnh sửa
          </v-btn>
          <v-btn text @click="viewDialog = false">Đóng</v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>

    <!-- Delete Confirmation Dialog -->
    <v-dialog v-model="deleteDialog" max-width="500px">
      <v-card>
        <v-card-title>Xác nhận xóa</v-card-title>
        <v-card-text>
          Bạn có chắc chắn muốn xóa giảng viên này không?
          <br><br>
          <strong>{{ itemToDelete?.full_name }} ({{ itemToDelete?.employee_code }})</strong>
        </v-card-text>
        <v-card-actions>
          <v-spacer></v-spacer>
          <v-btn text @click="deleteDialog = false">Hủy</v-btn>
          <v-btn color="error" @click="confirmDelete" :loading="deleting">Xóa</v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { instructorAPI, departmentAPI, userAPI } from '../services/api'
import { toast } from '@/composables/useToast'

const loading = ref(false)
const saving = ref(false)
const deleting = ref(false)
const search = ref('')
const instructors = ref([])
const departments = ref([])
const dialog = ref(false)
const viewDialog = ref(false)
const deleteDialog = ref(false)
const editingItem = ref(null)
const viewingItem = ref(null)
const itemToDelete = ref(null)
const valid = ref(false)
const form = ref()

const filters = ref({
  department: null,
  is_active: null
})

const statusOptions = [
  { text: 'Hoạt động', value: true },
  { text: 'Không hoạt động', value: false }
]

const formData = ref({
  employee_code: '',
  full_name: '',
  email: '',
  phone: '',
  department: null,
  max_teaching_hours: 40,
  is_active: true
})

const rules = {
  required: (value: any) => !!value || 'Trường này là bắt buộc',
  email: (value: string) => {
    if (!value) return true
    const pattern = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    return pattern.test(value) || 'Email không hợp lệ'
  }
}

const headers = [
  { title: 'Mã GV', key: 'employee_code' },
  { title: 'Họ tên', key: 'full_name' },
  { title: 'Email', key: 'email' },
  { title: 'Khoa', key: 'department_name' },
  { title: 'Tài khoản', key: 'user_username' },
  { title: 'Giờ dạy tối đa', key: 'max_teaching_hours' },
  { title: 'Trạng thái', key: 'is_active' },
  { title: 'Thao tác', key: 'actions', sortable: false }
]

onMounted(async () => {
  await loadDepartments()
  await loadInstructors()
})

const loadDepartments = async () => {
  try {
    const response = await departmentAPI.list()
    departments.value = response.data.results || response.data
  } catch (error) {
    console.error('Error loading departments:', error)
  }
}

const loadInstructors = async () => {
  loading.value = true
  try {
    const params = Object.fromEntries(
      Object.entries(filters.value).filter(([_, value]) => value !== null && value !== '')
    )

    const response = await instructorAPI.list(params)
    instructors.value = response.data.results || response.data
  } catch (error) {
    console.error('Error loading instructors:', error)
  } finally {
    loading.value = false
  }
}

const openCreateDialog = () => {
  editingItem.value = null
  formData.value = {
    employee_code: '',
    full_name: '',
    email: '',
    phone: '',
    department: null,
    max_teaching_hours: 40,
    is_active: true
  }
  dialog.value = true
}

const editInstructor = (item: any) => {
  editingItem.value = item
  formData.value = { ...item }
  dialog.value = true
}

const editFromView = () => {
  viewDialog.value = false
  editInstructor(viewingItem.value)
}

const viewInstructor = (item: any) => {
  viewingItem.value = item
  viewDialog.value = true
}

const deleteInstructor = (item: any) => {
  itemToDelete.value = item
  deleteDialog.value = true
}

const linkUser = async (item: any) => {
  // For now, we'll try to find a user with the same username as employee_code
  try {
    const usersResponse = await userAPI.list({ search: item.employee_code })
    const users = usersResponse.data.results || usersResponse.data

    const matchingUser = users.find((user: any) => user.username === item.employee_code)

    if (matchingUser) {
      // Link the instructor with the user
      await instructorAPI.linkUser(item.id, { user_id: matchingUser.id })
      toast.success(`Đã liên kết giảng viên ${item.employee_code} với tài khoản ${matchingUser.username}`)
      await loadInstructors()
    } else {
      toast.warning(`Không tìm thấy tài khoản với username: ${item.employee_code}`)
    }
  } catch (error: any) {
    console.error('Error linking user:', error)
    const errorMessage = error.response?.data?.error || 'Lỗi khi liên kết tài khoản'
    toast.error(errorMessage)
  }
}

const saveInstructor = async () => {
  const { valid: isValid } = await form.value.validate()
  if (!isValid) return

  saving.value = true
  try {
    if (editingItem.value) {
      await instructorAPI.update(editingItem.value.id, formData.value)
    } else {
      await instructorAPI.create(formData.value)
    }

    await loadInstructors()
    closeDialog()
  } catch (error) {
    console.error('Error saving instructor:', error)
  } finally {
    saving.value = false
  }
}

const confirmDelete = async () => {
  if (!itemToDelete.value) return

  deleting.value = true
  try {
    await instructorAPI.delete(itemToDelete.value.id)
    await loadInstructors()
    deleteDialog.value = false
    itemToDelete.value = null
  } catch (error) {
    console.error('Error deleting instructor:', error)
  } finally {
    deleting.value = false
  }
}

const closeDialog = () => {
  dialog.value = false
  editingItem.value = null
  form.value?.resetValidation()
}
</script>
