<template>
  <div>
    <!-- Access Denied Alert -->
    <v-alert
      v-if="$route.query.error === 'access_denied'"
      type="error"
      variant="tonal"
      closable
      class="mb-4"
    >
      <v-icon class="mr-2">mdi-shield-alert</v-icon>
      <strong>T<PERSON>y cập bị từ chối:</strong> {{ $route.query.message }}
    </v-alert>

    <v-row>
      <v-col cols="12">
        <div class="d-flex justify-space-between align-center mb-4">
          <h1 class="text-h4">Dashboard</h1>
          <div class="d-flex align-center">
            <v-chip v-if="authStore.user" color="primary" class="mr-2">
              <v-icon start>mdi-account</v-icon>
              {{ authStore.user.username }}
            </v-chip>
            <v-chip v-if="userRole" color="info">
              <v-icon start>mdi-shield-account</v-icon>
              {{ getRoleName(userRole) }}
            </v-chip>
          </div>
        </div>
      </v-col>
    </v-row>

    <!-- Statistics Cards -->
    <v-row>
      <v-col cols="12" sm="6" md="3">
        <v-card color="primary" dark>
          <v-card-text>
            <div class="d-flex align-center">
              <v-icon size="40" class="mr-4">mdi-timetable</v-icon>
              <div>
                <div class="text-h5">{{ stats.totalSchedules }}</div>
                <div>Lịch giảng dạy</div>
              </div>
            </div>
          </v-card-text>
        </v-card>
      </v-col>

      <v-col cols="12" sm="6" md="3">
        <v-card color="success" dark>
          <v-card-text>
            <div class="d-flex align-center">
              <v-icon size="40" class="mr-4">mdi-account-tie</v-icon>
              <div>
                <div class="text-h5">{{ stats.totalInstructors }}</div>
                <div>Giảng viên</div>
              </div>
            </div>
          </v-card-text>
        </v-card>
      </v-col>

      <v-col cols="12" sm="6" md="3">
        <v-card color="info" dark>
          <v-card-text>
            <div class="d-flex align-center">
              <v-icon size="40" class="mr-4">mdi-door</v-icon>
              <div>
                <div class="text-h5">{{ stats.totalRooms }}</div>
                <div>Phòng học</div>
              </div>
            </div>
          </v-card-text>
        </v-card>
      </v-col>

      <v-col cols="12" sm="6" md="3">
        <v-card color="warning" dark>
          <v-card-text>
            <div class="d-flex align-center">
              <v-icon size="40" class="mr-4">mdi-alert</v-icon>
              <div>
                <div class="text-h5">{{ stats.totalConflicts }}</div>
                <div>Xung đột</div>
              </div>
            </div>
          </v-card-text>
        </v-card>
      </v-col>
    </v-row>

    <!-- Quick Actions -->
    <v-row class="mt-4">
      <v-col cols="12">
        <v-card>
          <v-card-title>Thao tác nhanh</v-card-title>
          <v-card-text>
            <v-row>
              <v-col cols="12" sm="6" md="3">
                <v-btn
                  color="primary"
                  block
                  size="large"
                  to="/schedules/create"
                  prepend-icon="mdi-plus"
                >
                  Tạo lịch mới
                </v-btn>
              </v-col>
              <v-col cols="12" sm="6" md="3">
                <v-btn
                  color="success"
                  block
                  size="large"
                  to="/calendar"
                  prepend-icon="mdi-calendar"
                >
                  Xem lịch
                </v-btn>
              </v-col>
              <v-col cols="12" sm="6" md="3">
                <v-btn
                  color="info"
                  block
                  size="large"
                  to="/schedules"
                  prepend-icon="mdi-timetable"
                >
                  Quản lý lịch
                </v-btn>
              </v-col>
              <v-col cols="12" sm="6" md="3">
                <v-btn
                  color="warning"
                  block
                  size="large"
                  @click="checkConflicts"
                  prepend-icon="mdi-alert-circle"
                >
                  Kiểm tra xung đột
                </v-btn>
              </v-col>
            </v-row>
          </v-card-text>
        </v-card>
      </v-col>
    </v-row>

    <!-- Recent Schedules -->
    <v-row class="mt-4">
      <v-col cols="12" md="8">
        <v-card>
          <v-card-title>Lịch giảng dạy gần đây</v-card-title>
          <v-card-text>
            <v-data-table
              :headers="scheduleHeaders"
              :items="recentSchedules"
              :loading="loading"
              class="elevation-1"
            >
              <template v-slot:item.schedule_date="{ item }">
                {{ formatDate(item.schedule_date) }}
              </template>
              <template v-slot:item.status="{ item }">
                <v-chip
                  :color="getStatusColor(item.status)"
                  size="small"
                >
                  {{ getStatusText(item.status) }}
                </v-chip>
              </template>
            </v-data-table>
          </v-card-text>
        </v-card>
      </v-col>

      <v-col cols="12" md="4">
        <!-- User Profile Card -->
        <UserProfileCard
          :user="authStore.user"
          @change-password="openChangePasswordDialog"
          class="mb-4"
        />

        <v-card>
          <v-card-title>Thông báo</v-card-title>
          <v-card-text>
            <v-list>
              <v-list-item v-if="stats.totalConflicts > 0">
                <v-list-item-title class="text-warning">
                  Có {{ stats.totalConflicts }} xung đột cần xử lý
                </v-list-item-title>
              </v-list-item>
              <v-list-item v-else>
                <v-list-item-title class="text-success">
                  Không có xung đột nào
                </v-list-item-title>
              </v-list-item>
            </v-list>
          </v-card-text>
        </v-card>

        <!-- Role Info -->
        <v-card class="mt-4" v-if="userRole">
          <v-card-title>Thông tin vai trò & quyền hạn</v-card-title>
          <v-card-text>
            <div class="mb-3">
              <v-chip color="primary" size="small">
                <v-icon start>mdi-shield-account</v-icon>
                {{ getRoleName(userRole) }}
              </v-chip>
              <v-chip v-if="isSuperAdmin" color="error" size="small" class="ml-2">
                <v-icon start>mdi-crown</v-icon>
                Quản trị tối cao
              </v-chip>
              <v-chip v-else-if="isAdmin" color="warning" size="small" class="ml-2">
                <v-icon start>mdi-shield-star</v-icon>
                Quản trị viên
              </v-chip>
              <v-chip v-else-if="isTeacher" color="info" size="small" class="ml-2">
                <v-icon start>mdi-account-tie</v-icon>
                Giảng viên
              </v-chip>
              <v-chip v-else-if="isStaff" color="success" size="small" class="ml-2">
                <v-icon start>mdi-account-cog</v-icon>
                Nhân viên
              </v-chip>
            </div>

            <v-divider class="my-3"></v-divider>

            <div class="text-caption mb-2">Quyền hạn:</div>
            <v-chip-group column>
              <v-chip v-if="canManageUsers" color="red" size="x-small">
                <v-icon start size="16">mdi-account-group</v-icon>
                Quản lý người dùng
              </v-chip>
              <v-chip v-if="canManageSchedules" color="blue" size="x-small">
                <v-icon start size="16">mdi-calendar</v-icon>
                Quản lý lịch học
              </v-chip>
              <v-chip v-if="canManageBasicData" color="green" size="x-small">
                <v-icon start size="16">mdi-database</v-icon>
                Quản lý dữ liệu
              </v-chip>
              <v-chip v-if="canManageSystem" color="purple" size="x-small">
                <v-icon start size="16">mdi-cog</v-icon>
                Quản trị hệ thống
              </v-chip>
            </v-chip-group>
          </v-card-text>
        </v-card>
      </v-col>
    </v-row>

    <!-- Change Password Dialog -->
    <ChangePasswordDialog
      v-model="showChangePasswordDialog"
      @success="onPasswordChanged"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { scheduleAPI } from '../services/api'
import { useAuthStore } from '../stores/auth'
import { useSimpleAuth } from '../services/simpleAuth'
import UserProfileCard from '../components/UserProfileCard.vue'
import ChangePasswordDialog from '../components/ChangePasswordDialog.vue'
import { toast } from '@/composables/useToast'

const router = useRouter()
const authStore = useAuthStore()
const {
  userRole,
  isSuperAdmin,
  isAdmin,
  isTeacher,
  isStaff,
  canManageUsers,
  canManageSchedules,
  canManageBasicData,
  canManageSystem
} = useSimpleAuth()

const loading = ref(false)
const showChangePasswordDialog = ref(false)
const stats = ref({
  totalSchedules: 0,
  totalInstructors: 3,
  totalRooms: 4,
  totalConflicts: 0
})

const recentSchedules = ref([])

const scheduleHeaders = [
  { title: 'Ngày', key: 'schedule_date' },
  { title: 'Ca học', key: 'time_slot_name' },
  { title: 'Lớp', key: 'class_name' },
  { title: 'Môn học', key: 'subject_name' },
  { title: 'Giảng viên', key: 'instructor_name' },
  { title: 'Phòng', key: 'room_name' },
  { title: 'Trạng thái', key: 'status' }
]

onMounted(async () => {
  await loadDashboardData()
})

const loadDashboardData = async () => {
  loading.value = true
  try {
    // Load recent schedules
    const schedulesResponse = await scheduleAPI.list({ page_size: 10 })
    recentSchedules.value = schedulesResponse.data.results || schedulesResponse.data.slice(0, 10)
    stats.value.totalSchedules = schedulesResponse.data.count || schedulesResponse.data.length

    // Load conflicts
    const conflictsResponse = await scheduleAPI.conflicts()
    stats.value.totalConflicts = conflictsResponse.data.length
  } catch (error) {
    console.error('Error loading dashboard data:', error)
    // Set mock data if API fails
    stats.value.totalSchedules = 0
    recentSchedules.value = []
  } finally {
    loading.value = false
  }
}

const checkConflicts = async () => {
  router.push('/schedules?tab=conflicts')
}

const openChangePasswordDialog = () => {
  showChangePasswordDialog.value = true
}

const onPasswordChanged = () => {
  toast.success('Mật khẩu đã được thay đổi thành công!')
}

const formatDate = (date: string) => {
  return new Date(date).toLocaleDateString('vi-VN')
}

const getStatusColor = (status: string) => {
  switch (status) {
    case 'SCHEDULED': return 'primary'
    case 'COMPLETED': return 'success'
    case 'CANCELLED': return 'error'
    default: return 'grey'
  }
}

const getStatusText = (status: string) => {
  switch (status) {
    case 'SCHEDULED': return 'Đã lên lịch'
    case 'COMPLETED': return 'Đã hoàn thành'
    case 'CANCELLED': return 'Đã hủy'
    default: return status
  }
}

const getRoleName = (role: string) => {
  const roleNames = {
    'super_admin': 'Quản trị tối cao',
    'admin': 'Quản trị viên',
    'admin_khoa': 'Quản lý khoa',
    'phong_dao_tao': 'Phòng đào tạo',
    'teacher': 'Giảng viên',
    'giang_vien': 'Giảng viên',
    'staff': 'Nhân viên'
  }
  return roleNames[role] || role
}
</script>
