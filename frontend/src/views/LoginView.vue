<template>
  <div class="login-container">
    <!-- Background particles -->
    <div class="particles">
      <div class="particle" v-for="i in 50" :key="i"></div>
    </div>

    <v-container class="fill-height" fluid>
      <v-row align="center" justify="center" class="fill-height">
        <v-col cols="12" sm="10" md="8" lg="6" xl="4">
          <!-- Main Login Card -->
          <v-card class="login-card elevation-24" rounded="xl">
            <!-- Header Section -->
            <div class="login-header">
              <div class="logo-section">
                <v-avatar size="80" class="logo-avatar">
                  <v-icon size="50" color="white">mdi-school</v-icon>
                </v-avatar>
                <h1 class="system-title">Hệ thống quản lý lịch giảng dạy</h1>
                <p class="system-subtitle">Đ<PERSON>ng nhập để tiếp tục</p>
              </div>
            </div>

            <!-- Login Form -->
            <v-card-text class="login-form-section">
              <v-form ref="loginForm" @submit.prevent="handleLogin">
                <div class="form-group">
                  <v-text-field
                    v-model="username"
                    label="Tên đăng nhập"
                    prepend-inner-icon="mdi-account-outline"
                    variant="outlined"
                    color="primary"
                    :rules="[rules.required]"
                    class="custom-input"
                    hide-details="auto"
                  ></v-text-field>
                </div>

                <div class="form-group">
                  <v-text-field
                    v-model="password"
                    label="Mật khẩu"
                    prepend-inner-icon="mdi-lock-outline"
                    :append-inner-icon="showPassword ? 'mdi-eye' : 'mdi-eye-off'"
                    :type="showPassword ? 'text' : 'password'"
                    variant="outlined"
                    color="primary"
                    :rules="[rules.required]"
                    class="custom-input"
                    hide-details="auto"
                    @click:append-inner="showPassword = !showPassword"
                  ></v-text-field>
                </div>

                <v-alert
                  v-if="errorMessage"
                  type="error"
                  variant="tonal"
                  class="mb-4"
                  closable
                  @click:close="errorMessage = ''"
                >
                  {{ errorMessage }}
                </v-alert>

                <v-btn
                  type="submit"
                  color="primary"
                  size="large"
                  block
                  :loading="authStore.isLoading"
                  class="login-btn"
                  rounded="lg"
                >
                  <v-icon start>mdi-login</v-icon>
                  Đăng nhập
                </v-btn>
              </v-form>
            </v-card-text>
          </v-card>

          <!-- Demo credentials card -->
          <v-card class="demo-card mt-6" variant="outlined" rounded="lg">
            <v-card-text class="text-center">
              <v-chip color="info" variant="tonal" class="mb-3">
                <v-icon start>mdi-information</v-icon>
                Thông tin đăng nhập demo
              </v-chip>
              <div class="demo-accounts">
                <div class="demo-account" @click="fillCredentials('admin', 'admin123')">
                  <v-icon color="red">mdi-shield-crown</v-icon>
                  <span><strong>Super Admin:</strong> admin / admin123</span>
                </div>
                <div class="demo-account" @click="fillCredentials('pdt001', 'pdt123')">
                  <v-icon color="blue">mdi-account-tie</v-icon>
                  <span><strong>Phòng Đào Tạo:</strong> pdt001 / pdt123</span>
                </div>
                <div class="demo-account" @click="fillCredentials('gv001', 'gv123')">
                  <v-icon color="green">mdi-account-school</v-icon>
                  <span><strong>Giảng viên:</strong> gv001 / gv123</span>
                </div>
              </div>
            </v-card-text>
          </v-card>
        </v-col>
      </v-row>
    </v-container>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '../stores/auth'

const router = useRouter()
const authStore = useAuthStore()

const username = ref('')
const password = ref('')
const errorMessage = ref('')
const showPassword = ref(false)
const loginForm = ref()

const rules = {
  required: (value: string) => !!value || 'Trường này là bắt buộc'
}

onMounted(() => {
  // Redirect to dashboard if already authenticated
  if (authStore.isAuthenticated) {
    router.push('/dashboard')
  }

  // Add particle animation
  createParticles()
})

const handleLogin = async () => {
  const { valid } = await loginForm.value.validate()

  if (!valid) {
    return
  }

  errorMessage.value = ''

  const result = await authStore.login(username.value, password.value)

  if (result.success) {
    router.push('/dashboard')
  } else {
    errorMessage.value = result.error || 'Đăng nhập thất bại'
  }
}

const fillCredentials = (user: string, pass: string) => {
  username.value = user
  password.value = pass
}

const createParticles = () => {
  const particles = document.querySelectorAll('.particle')
  particles.forEach((particle, index) => {
    const element = particle as HTMLElement
    element.style.left = Math.random() * 100 + '%'
    element.style.animationDelay = Math.random() * 20 + 's'
    element.style.animationDuration = (Math.random() * 10 + 10) + 's'
  })
}
</script>

<style scoped>
.login-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  position: relative;
  overflow: hidden;
}

.particles {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
}

.particle {
  position: absolute;
  width: 4px;
  height: 4px;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  animation: float 15s infinite linear;
}

@keyframes float {
  0% {
    transform: translateY(100vh) rotate(0deg);
    opacity: 0;
  }
  10% {
    opacity: 1;
  }
  90% {
    opacity: 1;
  }
  100% {
    transform: translateY(-100px) rotate(360deg);
    opacity: 0;
  }
}

.login-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  overflow: hidden;
}

.login-header {
  background: linear-gradient(135deg, #1976d2 0%, #1565c0 100%);
  padding: 40px 30px;
  text-align: center;
  color: white;
}

.logo-avatar {
  background: rgba(255, 255, 255, 0.2);
  margin-bottom: 20px;
}

.system-title {
  font-size: 1.8rem;
  font-weight: 600;
  margin-bottom: 8px;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.system-subtitle {
  font-size: 1rem;
  opacity: 0.9;
  margin: 0;
}

.login-form-section {
  padding: 40px 30px;
}

.form-group {
  margin-bottom: 24px;
}

.custom-input :deep(.v-field) {
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.custom-input :deep(.v-field--focused) {
  box-shadow: 0 4px 12px rgba(25, 118, 210, 0.3);
}

.login-btn {
  height: 56px;
  font-size: 1.1rem;
  font-weight: 600;
  text-transform: none;
  box-shadow: 0 4px 12px rgba(25, 118, 210, 0.4);
  transition: all 0.3s ease;
}

.login-btn:hover {
  box-shadow: 0 6px 16px rgba(25, 118, 210, 0.5);
  transform: translateY(-2px);
}

.demo-card {
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.3);
}

.demo-accounts {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.demo-account {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 16px;
  border-radius: 8px;
  background: rgba(255, 255, 255, 0.7);
  cursor: pointer;
  transition: all 0.3s ease;
  border: 1px solid rgba(0, 0, 0, 0.1);
}

.demo-account:hover {
  background: rgba(255, 255, 255, 0.9);
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.demo-account span {
  font-size: 0.9rem;
}

/* Responsive */
@media (max-width: 600px) {
  .login-header {
    padding: 30px 20px;
  }

  .system-title {
    font-size: 1.5rem;
  }

  .login-form-section {
    padding: 30px 20px;
  }

  .demo-accounts {
    gap: 8px;
  }

  .demo-account {
    padding: 10px 12px;
  }

  .demo-account span {
    font-size: 0.8rem;
  }
}
</style>
