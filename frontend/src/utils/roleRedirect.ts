import { useAuthStore } from '@/stores/auth'

/**
 * Get the appropriate dashboard route based on user role
 */
export function getDashboardRoute(): string {
  const authStore = useAuthStore()
  
  if (!authStore.user) {
    return '/dashboard' // Default fallback
  }
  
  const userRole = authStore.user.role_name
  
  // Teacher roles should go to teacher dashboard
  if (userRole === 'teacher' || userRole === 'giang_vien') {
    console.log('Redirecting teacher to teacher dashboard, role:', userRole)
    return '/teacher-dashboard'
  }

  console.log('Redirecting to admin dashboard, role:', userRole)
  
  // All other roles go to admin dashboard
  return '/dashboard'
}

/**
 * Redirect user to appropriate dashboard based on role
 */
export function redirectToDashboard(router: any) {
  const route = getDashboardRoute()
  router.push(route)
}
