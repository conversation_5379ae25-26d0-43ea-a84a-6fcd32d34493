<script setup lang="ts">
import { onMounted } from 'vue'
import { useAuthStore } from './stores/auth'
import AppLayout from './components/AppLayout.vue'

const authStore = useAuthStore()

onMounted(() => {
  if (authStore.token) {
    authStore.fetchUser()
  }
})
</script>

<template>
  <v-app>
    <AppLayout />
  </v-app>
</template>

<style>
/* Global styles */
.v-application {
  font-family: 'Roboto', sans-serif;
}

/* Remove default margins and paddings for full screen views */
html, body {
  margin: 0;
  padding: 0;
  overflow-x: hidden;
}

.v-application {
  margin: 0;
  padding: 0;
}

/* Ensure full height for containers */
.v-main {
  min-height: 100vh;
}

/* Remove container padding for non-authenticated views */
.v-main .v-container {
  padding: 24px;
}

/* Full screen views should have no padding */
.home-container,
.login-container {
  margin: 0;
  padding: 0;
  width: 100vw;
  min-height: 100vh;
}
</style>
