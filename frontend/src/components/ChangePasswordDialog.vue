<template>
  <v-dialog v-model="dialog" max-width="500px" persistent>
    <v-card rounded="lg">
      <v-card-title class="text-h5 pa-6 bg-primary text-white">
        <v-icon start>mdi-lock-reset</v-icon>
        <PERSON><PERSON> mật khẩu
      </v-card-title>

      <v-card-text class="pa-6">
        <v-form ref="passwordForm" @submit.prevent="handleChangePassword">
          <div class="form-group">
            <v-text-field
              v-model="form.oldPassword"
              label="Mật khẩu hiện tại"
              prepend-inner-icon="mdi-lock-outline"
              :append-inner-icon="showOldPassword ? 'mdi-eye' : 'mdi-eye-off'"
              :type="showOldPassword ? 'text' : 'password'"
              variant="outlined"
              color="primary"
              :rules="[rules.required]"
              class="mb-4"
              @click:append-inner="showOldPassword = !showOldPassword"
            ></v-text-field>
          </div>

          <div class="form-group">
            <v-text-field
              v-model="form.newPassword"
              label="Mật khẩu mới"
              prepend-inner-icon="mdi-lock-plus-outline"
              :append-inner-icon="showNewPassword ? 'mdi-eye' : 'mdi-eye-off'"
              :type="showNewPassword ? 'text' : 'password'"
              variant="outlined"
              color="primary"
              :rules="[rules.required, rules.minLength]"
              class="mb-4"
              @click:append-inner="showNewPassword = !showNewPassword"
            ></v-text-field>
          </div>

          <div class="form-group">
            <v-text-field
              v-model="form.confirmPassword"
              label="Xác nhận mật khẩu mới"
              prepend-inner-icon="mdi-lock-check-outline"
              :append-inner-icon="showConfirmPassword ? 'mdi-eye' : 'mdi-eye-off'"
              :type="showConfirmPassword ? 'text' : 'password'"
              variant="outlined"
              color="primary"
              :rules="[rules.required, rules.passwordMatch]"
              class="mb-4"
              @click:append-inner="showConfirmPassword = !showConfirmPassword"
            ></v-text-field>
          </div>

          <v-alert
            v-if="errorMessage"
            type="error"
            variant="tonal"
            class="mb-4"
            closable
            @click:close="errorMessage = ''"
          >
            {{ errorMessage }}
          </v-alert>

          <v-alert
            v-if="successMessage"
            type="success"
            variant="tonal"
            class="mb-4"
            closable
            @click:close="successMessage = ''"
          >
            {{ successMessage }}
          </v-alert>
        </v-form>
      </v-card-text>

      <v-card-actions class="pa-6 pt-0">
        <v-spacer></v-spacer>
        <v-btn
          variant="outlined"
          @click="closeDialog"
          :disabled="loading"
        >
          Hủy
        </v-btn>
        <v-btn
          color="primary"
          :loading="loading"
          @click="handleChangePassword"
        >
          <v-icon start>mdi-check</v-icon>
          Thay đổi
        </v-btn>
      </v-card-actions>
    </v-card>
  </v-dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import axios from 'axios'

interface Props {
  modelValue: boolean
}

interface Emits {
  (e: 'update:modelValue', value: boolean): void
  (e: 'success'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const dialog = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const passwordForm = ref()
const loading = ref(false)
const errorMessage = ref('')
const successMessage = ref('')

const showOldPassword = ref(false)
const showNewPassword = ref(false)
const showConfirmPassword = ref(false)

const form = ref({
  oldPassword: '',
  newPassword: '',
  confirmPassword: ''
})

const rules = {
  required: (value: string) => !!value || 'Trường này là bắt buộc',
  minLength: (value: string) => !value || value.length >= 8 || 'Mật khẩu phải có ít nhất 8 ký tự',
  passwordMatch: (value: string) => value === form.value.newPassword || 'Mật khẩu xác nhận không khớp'
}

watch(dialog, (newValue) => {
  if (newValue) {
    resetForm()
  }
})

const resetForm = () => {
  form.value = {
    oldPassword: '',
    newPassword: '',
    confirmPassword: ''
  }
  errorMessage.value = ''
  successMessage.value = ''
  showOldPassword.value = false
  showNewPassword.value = false
  showConfirmPassword.value = false
}

const closeDialog = () => {
  dialog.value = false
}

const handleChangePassword = async () => {
  const { valid } = await passwordForm.value.validate()
  
  if (!valid) {
    return
  }

  loading.value = true
  errorMessage.value = ''
  successMessage.value = ''

  try {
    await axios.post('/api/v1/auth/change-password/', {
      old_password: form.value.oldPassword,
      new_password: form.value.newPassword
    })

    successMessage.value = 'Mật khẩu đã được thay đổi thành công!'
    
    setTimeout(() => {
      emit('success')
      closeDialog()
    }, 1500)

  } catch (error: any) {
    errorMessage.value = error.response?.data?.error || 'Có lỗi xảy ra khi thay đổi mật khẩu'
  } finally {
    loading.value = false
  }
}
</script>

<style scoped>
.form-group :deep(.v-field) {
  border-radius: 8px;
}

.form-group :deep(.v-field--focused) {
  box-shadow: 0 2px 8px rgba(25, 118, 210, 0.2);
}
</style>
