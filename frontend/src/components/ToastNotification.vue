<template>
  <v-snackbar
    v-model="show"
    :color="color"
    :timeout="timeout"
    location="top right"
    variant="elevated"
    class="toast-notification"
  >
    <div class="d-flex align-center">
      <v-icon :icon="icon" class="mr-3"></v-icon>
      <span>{{ message }}</span>
    </div>
    
    <template v-slot:actions>
      <v-btn
        variant="text"
        icon="mdi-close"
        @click="show = false"
      ></v-btn>
    </template>
  </v-snackbar>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'

interface Props {
  modelValue: boolean
  message: string
  type?: 'success' | 'error' | 'warning' | 'info'
  timeout?: number
}

interface Emits {
  (e: 'update:modelValue', value: boolean): void
}

const props = withDefaults(defineProps<Props>(), {
  type: 'success',
  timeout: 4000
})

const emit = defineEmits<Emits>()

const show = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const color = computed(() => {
  switch (props.type) {
    case 'success': return 'success'
    case 'error': return 'error'
    case 'warning': return 'warning'
    case 'info': return 'info'
    default: return 'success'
  }
})

const icon = computed(() => {
  switch (props.type) {
    case 'success': return 'mdi-check-circle'
    case 'error': return 'mdi-alert-circle'
    case 'warning': return 'mdi-alert'
    case 'info': return 'mdi-information'
    default: return 'mdi-check-circle'
  }
})
</script>

<style scoped>
.toast-notification :deep(.v-snackbar__wrapper) {
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15);
}

.toast-notification :deep(.v-snackbar__content) {
  padding: 16px 20px;
  font-weight: 500;
}
</style>
