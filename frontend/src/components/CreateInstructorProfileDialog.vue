<template>
  <v-dialog v-model="dialog" max-width="700px" persistent>
    <v-card rounded="lg">
      <v-card-title class="text-h5 pa-6 bg-primary text-white">
        <v-icon start>mdi-account-plus</v-icon>
        <PERSON><PERSON><PERSON> <PERSON>ồ sơ giảng viên
      </v-card-title>

      <v-card-text class="pa-6">
        <v-alert type="info" variant="tonal" class="mb-4">
          <v-icon start>mdi-information</v-icon>
          Vui lòng điền đầy đủ thông tin để tạo hồ sơ giảng viên. <PERSON><PERSON> khi tạo, quản trị viên sẽ xem xét và kích hoạt tài kho<PERSON>n.
        </v-alert>

        <v-form ref="profileForm" @submit.prevent="handleCreateProfile">
          <v-row>
            <v-col cols="12" md="6">
              <v-text-field
                v-model="form.employee_code"
                label="Mã giảng viên *"
                prepend-inner-icon="mdi-card-account-details"
                variant="outlined"
                :rules="[rules.required]"
                :error-messages="errors.employee_code"
                hint="Ví dụ: GV001, GV002..."
              ></v-text-field>
            </v-col>

            <v-col cols="12" md="6">
              <v-text-field
                v-model="form.full_name"
                label="Họ và tên *"
                prepend-inner-icon="mdi-account"
                variant="outlined"
                :rules="[rules.required]"
                :error-messages="errors.full_name"
              ></v-text-field>
            </v-col>

            <v-col cols="12" md="6">
              <v-text-field
                v-model="form.email"
                label="Email"
                prepend-inner-icon="mdi-email"
                variant="outlined"
                :rules="[rules.email]"
                :error-messages="errors.email"
                type="email"
              ></v-text-field>
            </v-col>

            <v-col cols="12" md="6">
              <v-text-field
                v-model="form.phone"
                label="Số điện thoại"
                prepend-inner-icon="mdi-phone"
                variant="outlined"
                :error-messages="errors.phone"
              ></v-text-field>
            </v-col>

            <v-col cols="12" md="6">
              <v-select
                v-model="form.department"
                :items="departments"
                item-title="name"
                item-value="id"
                label="Khoa *"
                prepend-inner-icon="mdi-domain"
                variant="outlined"
                :rules="[rules.required]"
                :error-messages="errors.department"
                :loading="loadingDepartments"
              ></v-select>
            </v-col>

            <v-col cols="12" md="6">
              <v-text-field
                v-model.number="form.max_teaching_hours"
                label="Số giờ dạy tối đa"
                prepend-inner-icon="mdi-clock"
                variant="outlined"
                type="number"
                :rules="[rules.required, rules.positiveNumber]"
                :error-messages="errors.max_teaching_hours"
                hint="Số giờ giảng dạy tối đa trong một tuần"
              ></v-text-field>
            </v-col>
          </v-row>

          <v-alert
            v-if="errorMessage"
            type="error"
            variant="tonal"
            class="mt-4"
            closable
            @click:close="errorMessage = ''"
          >
            {{ errorMessage }}
          </v-alert>

          <v-alert
            v-if="successMessage"
            type="success"
            variant="tonal"
            class="mt-4"
            closable
            @click:close="successMessage = ''"
          >
            {{ successMessage }}
          </v-alert>
        </v-form>
      </v-card-text>

      <v-card-actions class="pa-6 pt-0">
        <v-spacer></v-spacer>
        <v-btn
          variant="outlined"
          @click="closeDialog"
          :disabled="loading"
        >
          Hủy
        </v-btn>
        <v-btn
          color="primary"
          :loading="loading"
          @click="handleCreateProfile"
        >
          <v-icon start>mdi-check</v-icon>
          Tạo hồ sơ
        </v-btn>
      </v-card-actions>
    </v-card>
  </v-dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted } from 'vue'
import { instructorAPI, departmentAPI } from '@/services/api'
import { useAuthStore } from '@/stores/auth'

interface Props {
  modelValue: boolean
}

interface Emits {
  (e: 'update:modelValue', value: boolean): void
  (e: 'success'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const authStore = useAuthStore()

const dialog = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const profileForm = ref()
const loading = ref(false)
const loadingDepartments = ref(false)
const errorMessage = ref('')
const successMessage = ref('')
const errors = ref({})

const departments = ref([])

const form = ref({
  employee_code: '',
  full_name: authStore.user?.full_name || '',
  email: authStore.user?.email || '',
  phone: '',
  department: null,
  max_teaching_hours: 40
})

const rules = {
  required: (value: any) => !!value || 'Trường này là bắt buộc',
  email: (value: string) => {
    if (!value) return true
    const pattern = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    return pattern.test(value) || 'Email không hợp lệ'
  },
  positiveNumber: (value: number) => value > 0 || 'Giá trị phải lớn hơn 0'
}

watch(dialog, (newValue) => {
  if (newValue) {
    resetForm()
    loadDepartments()
  }
})

const resetForm = () => {
  form.value = {
    employee_code: '',
    full_name: authStore.user?.full_name || '',
    email: authStore.user?.email || '',
    phone: '',
    department: null,
    max_teaching_hours: 40
  }
  errors.value = {}
  errorMessage.value = ''
  successMessage.value = ''
}

const loadDepartments = async () => {
  try {
    loadingDepartments.value = true
    const response = await departmentAPI.list()
    departments.value = response.data.results || response.data
  } catch (error) {
    console.error('Error loading departments:', error)
    errorMessage.value = 'Lỗi khi tải danh sách khoa'
  } finally {
    loadingDepartments.value = false
  }
}

const closeDialog = () => {
  dialog.value = false
}

const handleCreateProfile = async () => {
  const { valid } = await profileForm.value.validate()
  
  if (!valid) {
    return
  }

  loading.value = true
  errorMessage.value = ''
  successMessage.value = ''
  errors.value = {}

  try {
    await instructorAPI.create(form.value)
    
    successMessage.value = 'Tạo hồ sơ giảng viên thành công!'
    
    setTimeout(() => {
      emit('success')
      closeDialog()
    }, 1500)

  } catch (error: any) {
    console.error('Error creating instructor profile:', error)
    
    if (error.response?.data) {
      // Handle field-specific errors
      if (typeof error.response.data === 'object') {
        errors.value = error.response.data
      } else {
        errorMessage.value = error.response.data.detail || error.response.data.error || 'Có lỗi xảy ra khi tạo hồ sơ'
      }
    } else {
      errorMessage.value = 'Có lỗi xảy ra khi tạo hồ sơ giảng viên'
    }
  } finally {
    loading.value = false
  }
}

onMounted(() => {
  loadDepartments()
})
</script>

<style scoped>
.form-group :deep(.v-field) {
  border-radius: 8px;
}

.form-group :deep(.v-field--focused) {
  box-shadow: 0 2px 8px rgba(25, 118, 210, 0.2);
}
</style>
