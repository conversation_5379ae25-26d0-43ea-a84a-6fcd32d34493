<template>
  <div>
    <!-- Navigation Bar -->
    <v-app-bar v-if="authStore.isAuthenticated" app color="primary" dark>
      <v-app-bar-nav-icon @click="drawer = !drawer"></v-app-bar-nav-icon>
      <v-toolbar-title><PERSON><PERSON> thống quản lý lịch giảng dạy</v-toolbar-title>
      <v-spacer></v-spacer>
      <v-menu>
        <template v-slot:activator="{ props }">
          <v-btn icon v-bind="props">
            <v-avatar size="36" color="white">
              <v-icon color="primary">mdi-account</v-icon>
            </v-avatar>
          </v-btn>
        </template>
        <v-list min-width="280">
          <!-- User Info -->
          <v-list-item class="user-info-item">
            <template v-slot:prepend>
              <v-avatar color="primary" size="40">
                <v-icon color="white">mdi-account</v-icon>
              </v-avatar>
            </template>
            <v-list-item-title class="font-weight-medium">
              {{ authStore.user?.full_name }}
            </v-list-item-title>
            <v-list-item-subtitle>
              {{ getRoleDisplayName(authStore.user?.role_name) }}
            </v-list-item-subtitle>
          </v-list-item>

          <v-divider></v-divider>

          <!-- Menu Items -->
          <v-list-item @click="openChangePasswordDialog" prepend-icon="mdi-lock-reset">
            <v-list-item-title>Thay đổi mật khẩu</v-list-item-title>
          </v-list-item>

          <v-list-item @click="logout" prepend-icon="mdi-logout">
            <v-list-item-title>Đăng xuất</v-list-item-title>
          </v-list-item>
        </v-list>
      </v-menu>
    </v-app-bar>

    <!-- Navigation Drawer -->
    <v-navigation-drawer v-if="authStore.isAuthenticated" v-model="drawer" app>
      <v-list>
        <!-- Simple Menu based on role -->
        <template v-for="(menuItem, index) in menu" :key="index">
          <!-- Menu item without children -->
          <v-list-item
            v-if="!menuItem.children"
            :to="menuItem.to"
            :prepend-icon="menuItem.icon"
          >
            <v-list-item-title>{{ menuItem.title }}</v-list-item-title>
          </v-list-item>

          <!-- Menu group with children -->
          <template v-else>
            <v-divider v-if="index > 0"></v-divider>
            <v-list-subheader>{{ menuItem.title }}</v-list-subheader>

            <v-list-item
              v-for="child in menuItem.children"
              :key="child.to"
              :to="child.to"
              :prepend-icon="child.icon"
            >
              <v-list-item-title>{{ child.title }}</v-list-item-title>
            </v-list-item>
          </template>
        </template>

      </v-list>
    </v-navigation-drawer>

    <!-- Main Content -->
    <v-main>
      <v-container fluid>
        <router-view />
      </v-container>
    </v-main>

    <!-- Change Password Dialog -->
    <ChangePasswordDialog
      v-model="showChangePasswordDialog"
      @success="onPasswordChanged"
    />

    <!-- Toast Notifications -->
    <ToastNotification
      v-for="toastItem in toasts"
      :key="toastItem.id"
      v-model="toastItem.show"
      :message="toastItem.message"
      :type="toastItem.type"
      :timeout="toastItem.timeout"
      @update:model-value="(value) => !value && removeToast(toastItem.id)"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '../stores/auth'
import { useSimpleAuth, getSimpleMenu } from '@/services/simpleAuth'
import ChangePasswordDialog from './ChangePasswordDialog.vue'
import ToastNotification from './ToastNotification.vue'
import { toast } from '@/composables/useToast'

const router = useRouter()
const authStore = useAuthStore()
const { userRole } = useSimpleAuth()
const { toasts, removeToast } = toast
const drawer = ref(false)
const showChangePasswordDialog = ref(false)

// Get simple menu based on role
const menu = computed(() => {
  return getSimpleMenu()
})

const getRoleDisplayName = (role: string | undefined) => {
  const roleNames = {
    'super_admin': 'Quản trị tối cao',
    'admin': 'Quản trị viên',
    'admin_khoa': 'Quản lý khoa',
    'phong_dao_tao': 'Phòng đào tạo',
    'teacher': 'Giảng viên',
    'giang_vien': 'Giảng viên',
    'staff': 'Nhân viên'
  }
  return roleNames[role as keyof typeof roleNames] || role || 'Người dùng'
}

const openChangePasswordDialog = () => {
  showChangePasswordDialog.value = true
}

const onPasswordChanged = () => {
  toast.success('Mật khẩu đã được thay đổi thành công!')
}

const logout = () => {
  authStore.logout()
  router.push('/login')
}
</script>

<style scoped>
.user-info-item {
  padding: 16px !important;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
}

.user-info-item :deep(.v-list-item-title) {
  font-size: 1rem;
  color: #1976d2;
}

.user-info-item :deep(.v-list-item-subtitle) {
  font-size: 0.875rem;
  color: #666;
  margin-top: 4px;
}
</style>
