<template>
  <v-dialog v-model="dialog" max-width="600px">
    <v-card>
      <v-card-title class="text-h5 bg-info text-white">
        <v-icon start>mdi-help-circle</v-icon>
        Hướng dẫn tạo hồ sơ giảng viên
      </v-card-title>

      <v-card-text class="pa-6">
        <div class="mb-4">
          <h3 class="text-h6 mb-2">Tại sao cần hồ sơ giảng viên?</h3>
          <p><PERSON><PERSON> sơ giảng viên là thông tin cần thiết để hệ thống có thể:</p>
          <ul class="ml-4">
            <li>Hiển thị lịch giảng dạy của bạn</li>
            <li>Quản lý thông tin cá nhân và liên hệ</li>
            <li><PERSON> dõi giờ giảng và khối lượng công việc</li>
            <li><PERSON><PERSON><PERSON> báo cáo và thống kê</li>
          </ul>
        </div>

        <div class="mb-4">
          <h3 class="text-h6 mb-2">Cách tạo hồ sơ:</h3>
          <v-stepper alt-labels>
            <v-stepper-header>
              <v-stepper-item title="Bước 1" value="1">Điền thông tin</v-stepper-item>
              <v-divider></v-divider>
              <v-stepper-item title="Bước 2" value="2">Chọn khoa</v-stepper-item>
              <v-divider></v-divider>
              <v-stepper-item title="Bước 3" value="3">Hoàn thành</v-stepper-item>
            </v-stepper-header>
          </v-stepper>
        </div>

        <v-alert type="info" variant="tonal">
          <strong>Lưu ý:</strong> Mã giảng viên sẽ tự động lấy từ tên đăng nhập của bạn và không thể thay đổi.
        </v-alert>
      </v-card-text>

      <v-card-actions class="pa-6 pt-0">
        <v-spacer></v-spacer>
        <v-btn variant="outlined" @click="closeDialog">
          Đóng
        </v-btn>
        <v-btn color="primary" @click="createProfile">
          Tạo hồ sơ ngay
        </v-btn>
      </v-card-actions>
    </v-card>
  </v-dialog>
</template>

<script setup lang="ts">
import { computed } from 'vue'

interface Props {
  modelValue: boolean
}

interface Emits {
  (e: 'update:modelValue', value: boolean): void
  (e: 'create-profile'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const dialog = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const closeDialog = () => {
  dialog.value = false
}

const createProfile = () => {
  emit('create-profile')
  closeDialog()
}
</script>

<style scoped>
.v-stepper {
  box-shadow: none;
  background: transparent;
}
</style>
