<template>
  <v-card class="user-profile-card" rounded="lg" elevation="4">
    <div class="profile-header">
      <v-avatar size="80" class="profile-avatar">
        <v-icon size="40" color="white">mdi-account</v-icon>
      </v-avatar>
      <div class="profile-info">
        <h2 class="profile-name">{{ user?.full_name }}</h2>
        <p class="profile-role">{{ getRoleDisplayName(user?.role_name) }}</p>
        <v-chip 
          :color="user?.is_active ? 'success' : 'error'" 
          size="small" 
          variant="tonal"
        >
          <v-icon start size="16">
            {{ user?.is_active ? 'mdi-check-circle' : 'mdi-close-circle' }}
          </v-icon>
          {{ user?.is_active ? 'Hoạt động' : 'Không hoạt động' }}
        </v-chip>
      </div>
    </div>

    <v-divider></v-divider>

    <v-card-text class="profile-details">
      <div class="detail-row">
        <v-icon color="primary" class="detail-icon">mdi-account-outline</v-icon>
        <div class="detail-content">
          <span class="detail-label">Tên đăng nhập</span>
          <span class="detail-value">{{ user?.username }}</span>
        </div>
      </div>

      <div class="detail-row" v-if="user?.email">
        <v-icon color="primary" class="detail-icon">mdi-email-outline</v-icon>
        <div class="detail-content">
          <span class="detail-label">Email</span>
          <span class="detail-value">{{ user?.email }}</span>
        </div>
      </div>

      <div class="detail-row" v-if="user?.date_joined">
        <v-icon color="primary" class="detail-icon">mdi-calendar-outline</v-icon>
        <div class="detail-content">
          <span class="detail-label">Ngày tham gia</span>
          <span class="detail-value">{{ formatDate(user?.date_joined) }}</span>
        </div>
      </div>
    </v-card-text>

    <v-card-actions class="profile-actions">
      <v-btn
        color="primary"
        variant="outlined"
        prepend-icon="mdi-lock-reset"
        @click="$emit('change-password')"
        block
      >
        Thay đổi mật khẩu
      </v-btn>
    </v-card-actions>
  </v-card>
</template>

<script setup lang="ts">
interface User {
  id: number
  username: string
  email?: string
  full_name: string
  role_name: string
  is_active: boolean
  date_joined?: string
}

interface Props {
  user: User | null
}

interface Emits {
  (e: 'change-password'): void
}

defineProps<Props>()
defineEmits<Emits>()

const getRoleDisplayName = (role: string | undefined) => {
  const roleNames = {
    'super_admin': 'Quản trị tối cao',
    'admin': 'Quản trị viên',
    'admin_khoa': 'Quản lý khoa',
    'phong_dao_tao': 'Phòng đào tạo',
    'teacher': 'Giảng viên',
    'giang_vien': 'Giảng viên',
    'staff': 'Nhân viên'
  }
  return roleNames[role as keyof typeof roleNames] || role || 'Người dùng'
}

const formatDate = (dateString: string | undefined) => {
  if (!dateString) return ''
  return new Date(dateString).toLocaleDateString('vi-VN', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  })
}
</script>

<style scoped>
.user-profile-card {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  overflow: hidden;
}

.profile-header {
  padding: 24px;
  display: flex;
  align-items: center;
  gap: 20px;
}

.profile-avatar {
  background: rgba(255, 255, 255, 0.2);
  border: 3px solid rgba(255, 255, 255, 0.3);
}

.profile-info {
  flex: 1;
}

.profile-name {
  font-size: 1.5rem;
  font-weight: 600;
  margin: 0 0 8px 0;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.profile-role {
  font-size: 1rem;
  margin: 0 0 12px 0;
  opacity: 0.9;
}

.profile-details {
  background: white;
  color: #333;
  padding: 20px 24px;
}

.detail-row {
  display: flex;
  align-items: center;
  gap: 16px;
  margin-bottom: 16px;
}

.detail-row:last-child {
  margin-bottom: 0;
}

.detail-icon {
  flex-shrink: 0;
}

.detail-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.detail-label {
  font-size: 0.875rem;
  color: #666;
  font-weight: 500;
}

.detail-value {
  font-size: 1rem;
  color: #333;
  font-weight: 600;
}

.profile-actions {
  background: white;
  padding: 16px 24px;
}

/* Responsive */
@media (max-width: 600px) {
  .profile-header {
    flex-direction: column;
    text-align: center;
    gap: 16px;
  }
  
  .profile-name {
    font-size: 1.25rem;
  }
  
  .detail-row {
    gap: 12px;
  }
}
</style>
