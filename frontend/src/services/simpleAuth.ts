import { ref, computed } from 'vue'
import { useAuthStore } from '@/stores/auth'

// Simple role-based permissions
// Role Matrix:
// Super Admin: Full access to everything
// Admin: Can manage users, schedules, basic data, system (almost everything except some super admin features)
// Staff: Can manage schedules and basic data (no user management)
// Teacher: Can only view their own schedule
export const ROLES = {
  SUPER_ADMIN: 'super_admin',
  ADMIN: 'admin',
  ADMIN_KHOA: 'admin_khoa', // Admin khoa acts like admin
  PHONG_DAO_TAO: 'phong_dao_tao', // Phong dao tao acts like admin
  TEACHER: 'teacher',
  GIANG_VIEN: 'giang_vien', // Giang vien acts like teacher
  STAFF: 'staff'
} as const

export type Role = typeof ROLES[keyof typeof ROLES]

// Simple permission check
export function useSimpleAuth() {
  const authStore = useAuthStore()

  const userRole = computed(() => authStore.user?.role_name as Role)
  const isLoggedIn = computed(() => authStore.isAuthenticated)
  
  // Role hierarchy check
  const isRole = (role: Role) => userRole.value === role
  const isSuperAdmin = computed(() => isRole(ROLES.SUPER_ADMIN))
  const isAdmin = computed(() =>
    isRole(ROLES.ADMIN) ||
    isRole(ROLES.ADMIN_KHOA) ||
    isRole(ROLES.PHONG_DAO_TAO) ||
    isSuperAdmin.value
  )
  const isTeacher = computed(() => isRole(ROLES.TEACHER) || isRole(ROLES.GIANG_VIEN))
  const isStaff = computed(() => isRole(ROLES.STAFF))
  
  // Simple permission checks
  const canManageUsers = computed(() => isSuperAdmin.value || isAdmin.value)
  const canManageSchedules = computed(() => isSuperAdmin.value || isAdmin.value || isStaff.value)
  const canViewOwnSchedule = computed(() => isLoggedIn.value) // Everyone can view their own
  const canManageBasicData = computed(() => isSuperAdmin.value || isAdmin.value || isStaff.value)
  const canManageSystem = computed(() => isSuperAdmin.value || isAdmin.value) // Admin can manage system too
  
  return {
    userRole,
    isLoggedIn,
    isSuperAdmin,
    isAdmin, 
    isTeacher,
    isStaff,
    canManageUsers,
    canManageSchedules,
    canViewOwnSchedule,
    canManageBasicData,
    canManageSystem
  }
}

// Simple menu structure based on roles
export function getSimpleMenu() {
  const { 
    isSuperAdmin, 
    isAdmin, 
    isTeacher, 
    isStaff,
    canManageUsers,
    canManageSchedules,
    canManageBasicData,
    canManageSystem
  } = useSimpleAuth()
  
  const menu = []
  
  // Dashboard - everyone
  menu.push({
    title: 'Dashboard',
    icon: 'mdi-view-dashboard',
    to: '/dashboard'
  })
  
  // Schedule section - everyone can access
  const scheduleMenu = {
    title: 'Lịch giảng dạy',
    icon: 'mdi-calendar-clock',
    children: []
  }

  // Everyone can view their own schedule
  scheduleMenu.children.push({
    title: 'Lịch của tôi',
    icon: 'mdi-calendar-account',
    to: '/my-schedule'
  })

  // Admins and staff can see all schedules and create new ones
  if (canManageSchedules.value) {
    scheduleMenu.children.push({
      title: 'Quản lý lịch',
      icon: 'mdi-calendar-month',
      to: '/schedules'
    })

    scheduleMenu.children.push({
      title: 'Tạo lịch mới',
      icon: 'mdi-calendar-plus',
      to: '/schedules/create'
    })

    scheduleMenu.children.push({
      title: 'Lịch tuần',
      icon: 'mdi-calendar-week',
      to: '/calendar'
    })
  }

  // Always add schedule menu since everyone should see it
  menu.push(scheduleMenu)
  
  // Basic data management
  if (canManageBasicData.value) {
    menu.push({
      title: 'Quản lý dữ liệu',
      icon: 'mdi-database',
      children: [
        {
          title: 'Giảng viên',
          icon: 'mdi-account-tie',
          to: '/instructors'
        },
        {
          title: 'Môn học', 
          icon: 'mdi-book-open-variant',
          to: '/subjects'
        },
        {
          title: 'Lớp học',
          icon: 'mdi-google-classroom', 
          to: '/classes'
        },
        {
          title: 'Phòng học',
          icon: 'mdi-door',
          to: '/rooms'
        }
      ]
    })
    
    menu.push({
      title: 'Cấu trúc tổ chức',
      icon: 'mdi-office-building',
      children: [
        {
          title: 'Cơ sở',
          icon: 'mdi-domain',
          to: '/campuses'
        },
        {
          title: 'Khoa/Phòng ban',
          icon: 'mdi-office-building',
          to: '/departments'
        },
        {
          title: 'Ngành học',
          icon: 'mdi-school',
          to: '/majors'
        }
      ]
    })
    
    menu.push({
      title: 'Quản lý thời gian',
      icon: 'mdi-clock-outline',
      children: [
        {
          title: 'Năm học',
          icon: 'mdi-calendar-range',
          to: '/academic-years'
        },
        {
          title: 'Học kỳ',
          icon: 'mdi-calendar-month',
          to: '/semesters'
        },
        {
          title: 'Ca học',
          icon: 'mdi-clock-outline',
          to: '/time-slots'
        }
      ]
    })
  }
  
  // User management - admins and super admins
  if (canManageUsers.value) {
    menu.push({
      title: 'Quản trị hệ thống',
      icon: 'mdi-shield-account',
      children: [
        {
          title: 'Người dùng',
          icon: 'mdi-account-group',
          to: '/user-management'
        },
        {
          title: 'Vai trò',
          icon: 'mdi-shield-account',
          to: '/role-management'
        },
        {
          title: 'Phân quyền',
          icon: 'mdi-shield-key',
          to: '/permission-management'
        }
      ]
    })
  }
  
  return menu
}

// Simple route guard - need to check auth store directly
export function canAccessRoute(path: string): boolean {
  // Check localStorage directly
  const token = localStorage.getItem('token')
  const userStr = localStorage.getItem('user')

  if (!token || !userStr) return false

  try {
    const user = JSON.parse(userStr)
    const userRole = user.role_name
    if (!userRole) return false

  // Super admin can access everything
  if (userRole === 'super_admin') return true

  // Public routes - everyone can access
  if (['/dashboard', '/', '/my-schedule'].includes(path)) return true

  // Admin roles (admin, admin_khoa, phong_dao_tao)
  const isAdminRole = ['admin', 'admin_khoa', 'phong_dao_tao'].includes(userRole)

  // Schedule routes
  if (['/schedules', '/schedules/create', '/calendar'].includes(path)) {
    return isAdminRole || userRole === 'staff'
  }
  if (path.startsWith('/schedules/')) {
    return isAdminRole || userRole === 'staff'
  }

  // Basic data routes
  const basicDataRoutes = [
    '/instructors', '/subjects', '/classes', '/rooms',
    '/campuses', '/departments', '/majors',
    '/academic-years', '/semesters', '/time-slots'
  ]
  if (basicDataRoutes.some(route => path.startsWith(route))) {
    return isAdminRole || userRole === 'staff'
  }

  // User management routes - Only admins
  const userRoutes = ['/user-management', '/role-management', '/permission-management']
  if (userRoutes.some(route => path.startsWith(route))) {
    return isAdminRole
  }

  // System routes - Only admins
  const systemRoutes = ['/system-management', '/reports']
  if (systemRoutes.some(route => path.startsWith(route))) {
    return isAdminRole
  }

    // Default allow for authenticated users on unknown routes
    return true
  } catch (error) {
    console.error('Error parsing user data:', error)
    return false
  }
}
