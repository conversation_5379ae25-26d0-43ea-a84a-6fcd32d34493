import { ref, computed } from 'vue'
import { roleAPI } from './api'

// Global state for user permissions
const userPermissions = ref<string[]>([])
const userRole = ref<any>(null)
const isLoading = ref(false)

// Permission service
export const usePermissions = () => {
  // Load user permissions from role
  const loadUserPermissions = async (roleId: number) => {
    if (!roleId) {
      userPermissions.value = []
      userRole.value = null
      return
    }

    try {
      isLoading.value = true

      // Get role details first
      const roleResponse = await roleAPI.get(roleId)
      userRole.value = roleResponse.data

      // Get permissions for this role
      const response = await roleAPI.getPermissions(roleId)
      userPermissions.value = response.data.map((p: any) => p.codename)

      console.log('Loaded permissions for role:', userRole.value.name, userPermissions.value)
    } catch (error) {
      console.error('Error loading user permissions:', error)
      userPermissions.value = []
      userRole.value = null
    } finally {
      isLoading.value = false
    }
  }

  // Check if user has specific permission
  const hasPermission = (permission: string): boolean => {
    if (!userPermissions.value.length) return false
    return userPermissions.value.includes(permission)
  }

  // Check if user has any of the permissions
  const hasAnyPermission = (permissions: string[]): boolean => {
    if (!userPermissions.value.length) return false
    return permissions.some(permission => userPermissions.value.includes(permission))
  }

  // Check if user has all permissions
  const hasAllPermissions = (permissions: string[]): boolean => {
    if (!userPermissions.value.length) return false
    return permissions.every(permission => userPermissions.value.includes(permission))
  }

  // Check if user has role
  const hasRole = (roleName: string): boolean => {
    return userRole.value?.name === roleName
  }

  // Check if user is super admin
  const isSuperAdmin = computed(() => {
    return userRole.value?.name === 'super_admin'
  })

  // Check if user is admin
  const isAdmin = computed(() => {
    return userRole.value?.name === 'admin' || isSuperAdmin.value
  })

  // Clear permissions (for logout)
  const clearPermissions = () => {
    userPermissions.value = []
    userRole.value = null
  }

  return {
    userPermissions: computed(() => userPermissions.value),
    userRole: computed(() => userRole.value),
    isLoading: computed(() => isLoading.value),
    isSuperAdmin,
    isAdmin,
    loadUserPermissions,
    hasPermission,
    hasAnyPermission,
    hasAllPermissions,
    hasRole,
    clearPermissions
  }
}

// Menu permission mappings
export const menuPermissions = {
  // Dashboard - everyone can access
  dashboard: [],

  // Schedule Management
  schedules: ['view_schedule'],
  mySchedule: ['view_schedule'], // Teachers can view their own schedule
  createSchedule: ['add_schedule'],
  
  // Basic Management
  campuses: ['view_campus'],
  departments: ['view_department'],
  majors: ['view_major'],
  academicYears: ['view_academicyear'],
  semesters: ['view_semester'],
  classes: ['view_class'],
  subjects: ['view_subject'],
  instructors: ['view_instructor'],
  rooms: ['view_room'],
  timeSlots: ['view_timeslot'],
  
  // User & Role Management
  userManagement: ['view_user'],
  roleManagement: ['view_role'],
  permissionManagement: ['view_role', 'manage_role_permissions'],
  
  // System Management
  systemManagement: ['manage_system'],
  reports: ['view_reports']
}

// Teacher-specific menu structure
export const teacherMenuStructure = [
  {
    title: 'Lịch giảng dạy',
    icon: 'mdi-calendar-account',
    to: '/teacher-dashboard',
    permissions: []
  }
]

// Menu structure with permissions
export const menuStructure = [
  {
    title: 'Dashboard',
    icon: 'mdi-view-dashboard',
    to: '/dashboard',
    permissions: menuPermissions.dashboard
  },
  {
    title: 'Lịch giảng dạy',
    icon: 'mdi-calendar-clock',
    children: [
      {
        title: 'Lịch của tôi',
        icon: 'mdi-calendar-account',
        to: '/my-schedule',
        permissions: menuPermissions.mySchedule
      },
      {
        title: 'Xem tất cả lịch',
        icon: 'mdi-calendar-month',
        to: '/schedules',
        permissions: menuPermissions.schedules
      },
      {
        title: 'Tạo lịch',
        icon: 'mdi-calendar-plus',
        to: '/create-schedule',
        permissions: menuPermissions.createSchedule
      }
    ]
  },
  {
    title: 'Quản lý cơ bản',
    icon: 'mdi-cog',
    children: [
      {
        title: 'Cơ sở',
        icon: 'mdi-domain',
        to: '/campuses',
        permissions: menuPermissions.campuses
      },
      {
        title: 'Khoa/Phòng ban',
        icon: 'mdi-office-building',
        to: '/departments',
        permissions: menuPermissions.departments
      },
      {
        title: 'Ngành học',
        icon: 'mdi-school',
        to: '/majors',
        permissions: menuPermissions.majors
      },
      {
        title: 'Năm học',
        icon: 'mdi-calendar-range',
        to: '/academic-years',
        permissions: menuPermissions.academicYears
      },
      {
        title: 'Học kỳ',
        icon: 'mdi-calendar-month',
        to: '/semesters',
        permissions: menuPermissions.semesters
      },
      {
        title: 'Lớp học',
        icon: 'mdi-google-classroom',
        to: '/classes',
        permissions: menuPermissions.classes
      },
      {
        title: 'Môn học',
        icon: 'mdi-book-open-page-variant',
        to: '/subjects',
        permissions: menuPermissions.subjects
      },
      {
        title: 'Giảng viên',
        icon: 'mdi-account-tie',
        to: '/instructors',
        permissions: menuPermissions.instructors
      },
      {
        title: 'Phòng học',
        icon: 'mdi-door',
        to: '/rooms',
        permissions: menuPermissions.rooms
      },
      {
        title: 'Ca học',
        icon: 'mdi-clock-outline',
        to: '/time-slots',
        permissions: menuPermissions.timeSlots
      }
    ]
  },
  {
    title: 'Quản lý hệ thống',
    icon: 'mdi-shield-account',
    children: [
      {
        title: 'Người dùng',
        icon: 'mdi-account-group',
        to: '/user-management',
        permissions: menuPermissions.userManagement
      },
      {
        title: 'Vai trò',
        icon: 'mdi-shield-account',
        to: '/role-management',
        permissions: menuPermissions.roleManagement
      },
      {
        title: 'Quyền hạn',
        icon: 'mdi-shield-key',
        to: '/permission-management',
        permissions: menuPermissions.permissionManagement
      }
    ]
  }
]

// Filter menu items based on permissions
export const getFilteredMenu = (permissions: string[], isSuperAdmin: boolean = false, userRole: any = null) => {
  if (isSuperAdmin) {
    return menuStructure // Super admin sees everything
  }

  // If user is teacher, show teacher-specific menu
  if (userRole?.name === 'teacher' || userRole?.name === 'giang_vien') {
    return teacherMenuStructure
  }

  const filterMenuItem = (item: any): any | null => {
    // If item has no permissions requirement, show it
    if (!item.permissions || item.permissions.length === 0) {
      if (item.children) {
        const filteredChildren = item.children
          .map(filterMenuItem)
          .filter((child: any) => child !== null)
        
        // Only show parent if it has visible children
        return filteredChildren.length > 0 ? { ...item, children: filteredChildren } : null
      }
      return item
    }

    // Check if user has required permissions
    const hasRequiredPermission = item.permissions.some((permission: string) => 
      permissions.includes(permission)
    )

    if (!hasRequiredPermission) {
      return null
    }

    // If item has children, filter them too
    if (item.children) {
      const filteredChildren = item.children
        .map(filterMenuItem)
        .filter((child: any) => child !== null)
      
      // Only show parent if it has visible children
      return filteredChildren.length > 0 ? { ...item, children: filteredChildren } : null
    }

    return item
  }

  return menuStructure
    .map(filterMenuItem)
    .filter(item => item !== null)
}
