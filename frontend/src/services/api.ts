import axios from 'axios'

const API_BASE_URL = 'http://127.0.0.1:8000/api/v1'

// Create axios instance
const api = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
})

// Add token to requests if available
api.interceptors.request.use((config) => {
  const token = localStorage.getItem('token')
  console.log('API Request:', config.method?.toUpperCase(), config.url, 'Token:', !!token)
  if (token) {
    config.headers.Authorization = `Token ${token}`
  }
  return config
})

// Handle response errors
api.interceptors.response.use(
  (response) => {
    console.log('API Response:', response.status, response.config.url)
    return response
  },
  (error) => {
    console.error('API Error:', error.message, error.response?.status, error.config?.url)
    if (error.response?.status === 401) {
      localStorage.removeItem('token')
      window.location.href = '/login'
    }
    return Promise.reject(error)
  }
)

// API endpoints
export const scheduleAPI = {
  list: (params?: any) => api.get('/schedules/', { params }),
  get: (id: number) => api.get(`/schedules/${id}/`),
  create: (data: any) => api.post('/schedules/', data),
  update: (id: number, data: any) => api.put(`/schedules/${id}/`, data),
  patch: (id: number, data: any) => api.patch(`/schedules/${id}/`, data),
  delete: (id: number) => api.delete(`/schedules/${id}/`),
  weekly: (date?: string) => api.get('/schedules/weekly/', { params: { date } }),
  conflicts: () => api.get('/schedules/conflicts/'),
  checkConflicts: (data: any) => api.post('/schedules/check_conflicts/', data),
  exportExcel: (params?: any) => api.get('/schedules/export_excel/', {
    params,
    responseType: 'blob'
  }),
  exportCampusWideExcel: (params?: any) => api.get('/schedules/export_campus_wide_excel/', {
    params,
    responseType: 'blob'
  }),
  exportClassExcel: (params?: any) => api.get('/schedules/export_class_excel/', {
    params,
    responseType: 'blob'
  }),
  exportRoomStatisticsExcel: (params?: any) => api.get('/schedules/export_room_statistics_excel/', {
    params,
    responseType: 'blob'
  }),
  exportPdf: (params?: any) => api.get('/schedules/export_pdf/', {
    params,
    responseType: 'blob'
  }),
  exportCampusWidePdf: (params?: any) => api.get('/schedules/export_campus_wide_pdf/', {
    params,
    responseType: 'blob'
  }),
  exportClassPdf: (params?: any) => api.get('/schedules/export_class_pdf/', {
    params,
    responseType: 'blob'
  }),
  exportRoomStatisticsPdf: (params?: any) => api.get('/schedules/export_room_statistics_pdf/', {
    params,
    responseType: 'blob'
  }),
}

export const timeSlotAPI = {
  list: (params?: any) => api.get('/time-slots/', { params }),
  get: (id: number) => api.get(`/time-slots/${id}/`),
  create: (data: any) => api.post('/time-slots/', data),
  update: (id: number, data: any) => api.put(`/time-slots/${id}/`, data),
  delete: (id: number) => api.delete(`/time-slots/${id}/`),
}

export const instructorAPI = {
  list: (params?: any) => api.get('/instructors/', { params }),
  get: (id: number) => api.get(`/instructors/${id}/`),
  create: (data: any) => api.post('/instructors/', data),
  update: (id: number, data: any) => api.put(`/instructors/${id}/`, data),
  delete: (id: number) => api.delete(`/instructors/${id}/`),
  myProfile: () => api.get('/instructors/my_profile/'),
  linkUser: (id: number, data: any) => api.post(`/instructors/${id}/link_user/`, data)
}

export const roomAPI = {
  list: (params?: any) => api.get('/rooms/', { params }),
  create: (data: any) => api.post('/rooms/', data),
  update: (id: number, data: any) => api.put(`/rooms/${id}/`, data),
  delete: (id: number) => api.delete(`/rooms/${id}/`),
}

export const subjectAPI = {
  list: (params?: any) => api.get('/subjects/', { params }),
  create: (data: any) => api.post('/subjects/', data),
  update: (id: number, data: any) => api.put(`/subjects/${id}/`, data),
  delete: (id: number) => api.delete(`/subjects/${id}/`),
}

export const classAPI = {
  list: (params?: any) => api.get('/classes/', { params }),
  create: (data: any) => api.post('/classes/', data),
  update: (id: number, data: any) => api.put(`/classes/${id}/`, data),
  delete: (id: number) => api.delete(`/classes/${id}/`),
}

export const lessonAPI = {
  list: (params?: any) => api.get('/lessons/', { params }),
  create: (data: any) => api.post('/lessons/', data),
  update: (id: number, data: any) => api.put(`/lessons/${id}/`, data),
  delete: (id: number) => api.delete(`/lessons/${id}/`),
}

export const academicYearAPI = {
  list: (params?: any) => api.get('/academic-years/', { params }),
  get: (id: number) => api.get(`/academic-years/${id}/`),
  create: (data: any) => api.post('/academic-years/', data),
  update: (id: number, data: any) => api.put(`/academic-years/${id}/`, data),
  delete: (id: number) => api.delete(`/academic-years/${id}/`),
  current: () => api.get('/academic-years/current/'),
}

export const semesterAPI = {
  list: (params?: any) => api.get('/semesters/', { params }),
  get: (id: number) => api.get(`/semesters/${id}/`),
  create: (data: any) => api.post('/semesters/', data),
  update: (id: number, data: any) => api.put(`/semesters/${id}/`, data),
  delete: (id: number) => api.delete(`/semesters/${id}/`),
  current: () => api.get('/semesters/current/'),
}

export const departmentAPI = {
  list: (params?: any) => api.get('/departments/', { params }),
  get: (id: number) => api.get(`/departments/${id}/`),
  create: (data: any) => api.post('/departments/', data),
  update: (id: number, data: any) => api.put(`/departments/${id}/`, data),
  delete: (id: number) => api.delete(`/departments/${id}/`),
}

export const campusAPI = {
  list: (params?: any) => api.get('/campuses/', { params }),
  get: (id: number) => api.get(`/campuses/${id}/`),
  create: (data: any) => api.post('/campuses/', data),
  update: (id: number, data: any) => api.put(`/campuses/${id}/`, data),
  delete: (id: number) => api.delete(`/campuses/${id}/`),
}

export const majorAPI = {
  list: (params?: any) => api.get('/majors/', { params }),
  get: (id: number) => api.get(`/majors/${id}/`),
  create: (data: any) => api.post('/majors/', data),
  update: (id: number, data: any) => api.put(`/majors/${id}/`, data),
  delete: (id: number) => api.delete(`/majors/${id}/`),
}

export const practiceGroupAPI = {
  list: (params?: any) => api.get('/practice-groups/', { params }),
  create: (data: any) => api.post('/practice-groups/', data),
  update: (id: number, data: any) => api.put(`/practice-groups/${id}/`, data),
  delete: (id: number) => api.delete(`/practice-groups/${id}/`),
}

export const userAPI = {
  list: (params?: any) => api.get('/users/', { params }),
  get: (id: number) => api.get(`/users/${id}/`),
  create: (data: any) => api.post('/users/', data),
  update: (id: number, data: any) => api.put(`/users/${id}/`, data),
  delete: (id: number) => api.delete(`/users/${id}/`),
  changePassword: (id: number, data: any) => api.post(`/users/${id}/change_password/`, data),
}

export const permissionAPI = {
  list: (params?: any) => api.get('/permissions/', { params }),
  get: (id: number) => api.get(`/permissions/${id}/`),
  create: (data: any) => api.post('/permissions/', data),
  update: (id: number, data: any) => api.put(`/permissions/${id}/`, data),
  delete: (id: number) => api.delete(`/permissions/${id}/`),
}

export const roleAPI = {
  list: (params?: any) => api.get('/roles/', { params }),
  get: (id: number) => api.get(`/roles/${id}/`),
  create: (data: any) => api.post('/roles/', data),
  update: (id: number, data: any) => api.put(`/roles/${id}/`, data),
  delete: (id: number) => api.delete(`/roles/${id}/`),
  assignPermissions: (id: number, permissionIds: number[]) => api.post(`/roles/${id}/assign_permissions/`, { permission_ids: permissionIds }),
  getPermissions: (id: number) => api.get(`/roles/${id}/permissions/`),
}

export default api
