import { ref } from 'vue'

interface ToastOptions {
  message: string
  type?: 'success' | 'error' | 'warning' | 'info'
  timeout?: number
}

const toasts = ref<Array<ToastOptions & { id: number; show: boolean }>>([])
let toastId = 0

export function useToast() {
  const showToast = (options: ToastOptions) => {
    const id = ++toastId
    const toast = {
      id,
      show: true,
      type: 'success' as const,
      timeout: 4000,
      ...options
    }
    
    toasts.value.push(toast)
    
    // Auto remove after timeout
    setTimeout(() => {
      removeToast(id)
    }, toast.timeout)
    
    return id
  }
  
  const removeToast = (id: number) => {
    const index = toasts.value.findIndex(toast => toast.id === id)
    if (index > -1) {
      toasts.value.splice(index, 1)
    }
  }
  
  const success = (message: string, timeout?: number) => {
    return showToast({ message, type: 'success', timeout })
  }
  
  const error = (message: string, timeout?: number) => {
    return showToast({ message, type: 'error', timeout })
  }
  
  const warning = (message: string, timeout?: number) => {
    return showToast({ message, type: 'warning', timeout })
  }
  
  const info = (message: string, timeout?: number) => {
    return showToast({ message, type: 'info', timeout })
  }
  
  const clear = () => {
    toasts.value = []
  }
  
  return {
    toasts,
    showToast,
    removeToast,
    success,
    error,
    warning,
    info,
    clear
  }
}

// Global toast instance
export const toast = useToast()
