import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import axios from 'axios'
import { usePermissions } from '@/services/permissions'

const API_BASE_URL = 'http://127.0.0.1:8001/api/v1'

export interface User {
  id: number
  username: string
  email: string
  full_name: string
  role: number
  role_name: string
  is_active: boolean
  is_staff?: boolean
  date_joined?: string
}

export const useAuthStore = defineStore('auth', () => {
  const user = ref<User | null>(null)
  const token = ref<string | null>(localStorage.getItem('token'))
  const isLoading = ref(false)

  // Initialize permissions service
  const { loadUserPermissions, clearPermissions } = usePermissions()

  const isAuthenticated = computed(() => !!token.value && !!user.value)

  // Load user from localStorage on init
  const storedUser = localStorage.getItem('user')
  if (storedUser) {
    try {
      user.value = JSON.parse(storedUser)
    } catch (error) {
      console.error('Error parsing stored user:', error)
      localStorage.removeItem('user')
    }
  }

  // Configure axios defaults
  if (token.value) {
    axios.defaults.headers.common['Authorization'] = `Token ${token.value}`
  }

  const login = async (username: string, password: string) => {
    isLoading.value = true
    try {
      const response = await axios.post(`${API_BASE_URL}/auth/login/`, {
        username,
        password
      })

      const { token: authToken, user: userData } = response.data

      token.value = authToken
      user.value = userData

      localStorage.setItem('token', authToken)
      localStorage.setItem('user', JSON.stringify(userData))
      axios.defaults.headers.common['Authorization'] = `Token ${authToken}`

      // Load user permissions
      if (userData.role) {
        await loadUserPermissions(userData.role)
      }

      return { success: true }
    } catch (error: any) {
      return {
        success: false,
        error: error.response?.data?.error || error.response?.data?.detail || 'Login failed'
      }
    } finally {
      isLoading.value = false
    }
  }

  const logout = () => {
    token.value = null
    user.value = null
    localStorage.removeItem('token')
    localStorage.removeItem('user')
    delete axios.defaults.headers.common['Authorization']

    // Clear permissions
    clearPermissions()
  }

  const fetchUser = async () => {
    if (!token.value) return

    try {
      const response = await axios.get(`${API_BASE_URL}/auth/user/`)
      user.value = response.data
      localStorage.setItem('user', JSON.stringify(response.data))

      // Load user permissions
      if (response.data.role) {
        await loadUserPermissions(response.data.role)
      }
    } catch (error) {
      console.error('Error fetching user:', error)
      logout()
    }
  }

  return {
    user,
    token,
    isLoading,
    isAuthenticated,
    login,
    logout,
    fetchUser
  }
})
