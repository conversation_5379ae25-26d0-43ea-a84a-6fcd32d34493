from rest_framework import viewsets, status, filters
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from rest_framework.exceptions import ValidationError
from django_filters.rest_framework import DjangoFilterBackend
from django.db.models import Q
from django.http import HttpResponse
from datetime import datetime, timedelta
import openpyxl
from openpyxl.styles import Font, Alignment, Border, Side, PatternFill
from openpyxl.utils import get_column_letter
import io
from reportlab.lib.pagesizes import A4, landscape
from reportlab.lib import colors
from reportlab.lib.units import mm
from reportlab.platypus import SimpleDocTemplate, Table, TableStyle, Paragraph, Spacer
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.lib.enums import TA_CENTER, TA_LEFT, TA_RIGHT
from reportlab.pdfbase import pdfmetrics
from reportlab.pdfbase.ttfonts import TTFont
import os

from core.models import (
    Permission, Role, User, Campus, Department, Major, AcademicYear, Semester,
    Class, Subject, Lesson, Instructor, Room, TimeSlot, PracticeGroup,
    Schedule, ScheduleConflict
)
from .serializers import (
    PermissionSerializer, RoleSerializer, RoleDetailSerializer, UserSerializer,
    CampusSerializer, DepartmentSerializer, MajorSerializer, AcademicYearSerializer,
    SemesterSerializer, ClassSerializer, SubjectSerializer, LessonSerializer,
    InstructorSerializer, RoomSerializer, TimeSlotSerializer, PracticeGroupSerializer,
    ScheduleSerializer, ScheduleConflictSerializer, SimpleOptionSerializer,
    ScheduleCreateSerializer
)

def register_vietnamese_fonts():
    """Register Vietnamese fonts for ReportLab"""
    try:
        # Try to find system fonts that support Vietnamese
        font_paths = [
            '/System/Library/Fonts/Arial.ttf',  # macOS
            '/System/Library/Fonts/Helvetica.ttc',  # macOS
            '/usr/share/fonts/truetype/dejavu/DejaVuSans.ttf',  # Linux
            '/Windows/Fonts/arial.ttf',  # Windows
            '/usr/share/fonts/TTF/DejaVuSans.ttf',  # Some Linux distributions
        ]

        font_registered = False
        for font_path in font_paths:
            if os.path.exists(font_path):
                try:
                    pdfmetrics.registerFont(TTFont('Vietnamese', font_path))
                    pdfmetrics.registerFont(TTFont('Vietnamese-Bold', font_path))
                    font_registered = True
                    break
                except:
                    continue

        if not font_registered:
            # Fallback: use built-in fonts with Unicode support
            return 'Helvetica', 'Helvetica-Bold'

        return 'Vietnamese', 'Vietnamese-Bold'

    except Exception as e:
        # Fallback to default fonts
        return 'Helvetica', 'Helvetica-Bold'


class PermissionViewSet(viewsets.ModelViewSet):
    queryset = Permission.objects.all()
    serializer_class = PermissionSerializer
    permission_classes = [IsAuthenticated]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter]
    filterset_fields = ['module']
    search_fields = ['name', 'codename', 'description']


class RoleViewSet(viewsets.ModelViewSet):
    queryset = Role.objects.prefetch_related('permissions').all()
    permission_classes = [IsAuthenticated]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter]
    search_fields = ['name', 'description']

    def get_serializer_class(self):
        if self.action in ['retrieve', 'update', 'partial_update']:
            return RoleDetailSerializer
        return RoleSerializer

    @action(detail=True, methods=['post'])
    def assign_permissions(self, request, pk=None):
        """Assign permissions to role"""
        role = self.get_object()
        permission_ids = request.data.get('permission_ids', [])

        if not isinstance(permission_ids, list):
            return Response({'permission_ids': 'Must be a list of permission IDs'},
                          status=status.HTTP_400_BAD_REQUEST)

        # Validate permission IDs
        valid_permissions = Permission.objects.filter(id__in=permission_ids)
        if len(valid_permissions) != len(permission_ids):
            return Response({'permission_ids': 'Some permission IDs are invalid'},
                          status=status.HTTP_400_BAD_REQUEST)

        # Assign permissions
        role.permissions.set(permission_ids)

        serializer = RoleDetailSerializer(role)
        return Response(serializer.data)

    @action(detail=True, methods=['get'])
    def permissions(self, request, pk=None):
        """Get role permissions"""
        role = self.get_object()
        permissions = role.permissions.all()
        serializer = PermissionSerializer(permissions, many=True)
        return Response(serializer.data)


class UserViewSet(viewsets.ModelViewSet):
    queryset = User.objects.all()
    serializer_class = UserSerializer
    permission_classes = [IsAuthenticated]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter]
    filterset_fields = ['role', 'is_active']
    search_fields = ['username', 'full_name', 'email']

    @action(detail=True, methods=['post'])
    def change_password(self, request, pk=None):
        """Change user password"""
        user = self.get_object()
        new_password = request.data.get('new_password')

        if not new_password:
            return Response({'new_password': 'Mật khẩu mới là bắt buộc'},
                          status=status.HTTP_400_BAD_REQUEST)

        if len(new_password) < 8:
            return Response({'new_password': 'Mật khẩu phải có ít nhất 8 ký tự'},
                          status=status.HTTP_400_BAD_REQUEST)

        user.set_password(new_password)
        user.save()

        return Response({'detail': 'Đổi mật khẩu thành công'},
                       status=status.HTTP_200_OK)


class CampusViewSet(viewsets.ModelViewSet):
    queryset = Campus.objects.all()
    serializer_class = CampusSerializer
    permission_classes = [IsAuthenticated]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter]
    filterset_fields = ['is_active']
    search_fields = ['code', 'name']


class DepartmentViewSet(viewsets.ModelViewSet):
    queryset = Department.objects.select_related('campus').all()
    serializer_class = DepartmentSerializer
    permission_classes = [IsAuthenticated]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter]
    filterset_fields = ['campus', 'is_active']
    search_fields = ['code', 'name']


class MajorViewSet(viewsets.ModelViewSet):
    queryset = Major.objects.select_related('department').all()
    serializer_class = MajorSerializer
    permission_classes = [IsAuthenticated]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter]
    filterset_fields = ['department', 'is_active']
    search_fields = ['code', 'name']


class AcademicYearViewSet(viewsets.ModelViewSet):
    queryset = AcademicYear.objects.all()
    serializer_class = AcademicYearSerializer
    permission_classes = [IsAuthenticated]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter]
    filterset_fields = ['is_current']
    search_fields = ['year_code']

    @action(detail=False, methods=['get'])
    def current(self, request):
        """Get current academic year"""
        current_year = AcademicYear.objects.filter(is_current=True).first()
        if current_year:
            serializer = self.get_serializer(current_year)
            return Response(serializer.data)
        return Response({'detail': 'No current academic year found'}, status=status.HTTP_404_NOT_FOUND)


class SemesterViewSet(viewsets.ModelViewSet):
    queryset = Semester.objects.select_related('academic_year').all()
    serializer_class = SemesterSerializer
    permission_classes = [IsAuthenticated]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter]
    filterset_fields = ['academic_year', 'is_current']
    search_fields = ['semester_code', 'name']

    @action(detail=False, methods=['get'])
    def current(self, request):
        """Get current semester"""
        current_semester = Semester.objects.filter(is_current=True).first()
        if current_semester:
            serializer = self.get_serializer(current_semester)
            return Response(serializer.data)
        return Response({'detail': 'No current semester found'}, status=status.HTTP_404_NOT_FOUND)


class ClassViewSet(viewsets.ModelViewSet):
    queryset = Class.objects.select_related('major').all()
    serializer_class = ClassSerializer
    permission_classes = [IsAuthenticated]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter]
    filterset_fields = ['major', 'year_level', 'is_active']
    search_fields = ['code', 'name']


class SubjectViewSet(viewsets.ModelViewSet):
    queryset = Subject.objects.select_related('department').all()
    serializer_class = SubjectSerializer
    permission_classes = [IsAuthenticated]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter]
    filterset_fields = ['department', 'credits', 'is_active']
    search_fields = ['code', 'name']


class LessonViewSet(viewsets.ModelViewSet):
    queryset = Lesson.objects.select_related('subject').all()
    serializer_class = LessonSerializer
    permission_classes = [IsAuthenticated]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter]
    filterset_fields = ['subject', 'lesson_type']
    search_fields = ['title']


class InstructorViewSet(viewsets.ModelViewSet):
    queryset = Instructor.objects.select_related('department', 'user').all()
    serializer_class = InstructorSerializer
    permission_classes = [IsAuthenticated]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter]
    filterset_fields = ['department', 'is_active']
    search_fields = ['employee_code', 'full_name', 'email']

    def perform_create(self, serializer):
        """Override create to automatically link with current user"""
        # Check if user already has an instructor profile
        if hasattr(self.request.user, 'instructor_profile') and self.request.user.instructor_profile:
            raise ValidationError({'error': 'User already has an instructor profile'})

        # Set employee_code to username if not provided
        if not serializer.validated_data.get('employee_code'):
            serializer.validated_data['employee_code'] = self.request.user.username

        # Link with current user
        serializer.save(user=self.request.user)

    @action(detail=False, methods=['get'])
    def my_profile(self, request):
        """Get instructor profile for current user"""
        try:
            instructor = Instructor.objects.select_related('department', 'user').get(
                user=request.user
            )
            serializer = self.get_serializer(instructor)
            return Response(serializer.data)
        except Instructor.DoesNotExist:
            return Response(
                {'error': 'Instructor profile not found for current user'},
                status=status.HTTP_404_NOT_FOUND
            )


class RoomViewSet(viewsets.ModelViewSet):
    queryset = Room.objects.select_related('campus').all()
    serializer_class = RoomSerializer
    permission_classes = [IsAuthenticated]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter]
    filterset_fields = ['campus', 'room_type', 'is_available']
    search_fields = ['code', 'name']


class TimeSlotViewSet(viewsets.ModelViewSet):
    queryset = TimeSlot.objects.all()
    serializer_class = TimeSlotSerializer
    permission_classes = [IsAuthenticated]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter]
    filterset_fields = ['session', 'is_active']
    search_fields = ['slot_name']


class PracticeGroupViewSet(viewsets.ModelViewSet):
    queryset = PracticeGroup.objects.select_related('class_obj', 'subject').all()
    serializer_class = PracticeGroupSerializer
    permission_classes = [IsAuthenticated]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter]
    filterset_fields = ['class_obj', 'subject']
    search_fields = ['class_obj__code', 'subject__code']


class ScheduleViewSet(viewsets.ModelViewSet):
    queryset = Schedule.objects.select_related(
        'semester', 'class_obj', 'subject', 'lesson', 'instructor',
        'room', 'time_slot', 'practice_group', 'created_by'
    ).all()
    permission_classes = [IsAuthenticated]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter]
    filterset_fields = [
        'semester', 'class_obj', 'subject', 'instructor', 'room',
        'schedule_date', 'status', 'lesson_type'
    ]
    search_fields = [
        'class_obj__code', 'subject__code', 'instructor__full_name',
        'room__code', 'lesson__title'
    ]

    def get_queryset(self):
        queryset = super().get_queryset()

        # If user is a teacher, only show their schedules
        user = self.request.user
        if hasattr(user, 'instructor_profile') and user.instructor_profile:
            queryset = queryset.filter(instructor=user.instructor_profile)
        elif user.role and user.role.name == 'teacher':
            # If user is teacher but no instructor profile, show no schedules
            queryset = queryset.none()

        return queryset

    def get_serializer_class(self):
        if self.action in ['create', 'update', 'partial_update']:
            return ScheduleCreateSerializer
        return ScheduleSerializer

    @action(detail=False, methods=['get'])
    def weekly(self, request):
        """Get weekly schedule"""
        from datetime import datetime, timedelta

        # Get date parameter or use current week
        date_str = request.query_params.get('date')
        if date_str:
            try:
                start_date = datetime.strptime(date_str, '%Y-%m-%d').date()
            except ValueError:
                return Response({'error': 'Invalid date format. Use YYYY-MM-DD'},
                              status=status.HTTP_400_BAD_REQUEST)
        else:
            start_date = datetime.now().date()
            start_date = start_date - timedelta(days=start_date.weekday())  # Monday

        end_date = start_date + timedelta(days=6)  # Sunday

        schedules = self.get_queryset().filter(
            schedule_date__range=[start_date, end_date]
        ).order_by('schedule_date', 'time_slot__start_time')

        serializer = self.get_serializer(schedules, many=True)
        return Response({
            'start_date': start_date,
            'end_date': end_date,
            'schedules': serializer.data
        })

    @action(detail=False, methods=['get'])
    def conflicts(self, request):
        """Get schedule conflicts"""
        conflicts = ScheduleConflict.objects.select_related(
            'schedule', 'conflict_with_schedule'
        ).filter(is_resolved=False)

        serializer = ScheduleConflictSerializer(conflicts, many=True)
        return Response(serializer.data)

    @action(detail=False, methods=['post'])
    def check_conflicts(self, request):
        """Check for conflicts before creating schedule"""
        from django.db.models import Q

        data = request.data
        conflicts = []

        # Check instructor conflicts
        instructor_conflicts = Schedule.objects.filter(
            instructor_id=data.get('instructor'),
            schedule_date=data.get('schedule_date'),
            time_slot_id=data.get('time_slot'),
            status='SCHEDULED'
        )

        if instructor_conflicts.exists():
            conflicts.append({
                'type': 'INSTRUCTOR',
                'message': 'Giảng viên đã có lịch dạy vào thời gian này',
                'conflicting_schedules': ScheduleSerializer(instructor_conflicts, many=True).data
            })

        # Check room conflicts
        room_conflicts = Schedule.objects.filter(
            room_id=data.get('room'),
            schedule_date=data.get('schedule_date'),
            time_slot_id=data.get('time_slot'),
            status='SCHEDULED'
        )

        if room_conflicts.exists():
            conflicts.append({
                'type': 'ROOM',
                'message': 'Phòng học đã được sử dụng vào thời gian này',
                'conflicting_schedules': ScheduleSerializer(room_conflicts, many=True).data
            })

        # Check class conflicts
        class_conflicts = Schedule.objects.filter(
            class_obj_id=data.get('class_obj'),
            schedule_date=data.get('schedule_date'),
            time_slot_id=data.get('time_slot'),
            status='SCHEDULED'
        )

        if class_conflicts.exists():
            conflicts.append({
                'type': 'CLASS',
                'message': 'Lớp học đã có lịch học vào thời gian này',
                'conflicting_schedules': ScheduleSerializer(class_conflicts, many=True).data
            })

        return Response({
            'has_conflicts': len(conflicts) > 0,
            'conflicts': conflicts
        })

    @action(detail=False, methods=['get'])
    def export_excel(self, request):
        """Export schedules to Excel format"""
        try:
            # Get query parameters
            date_str = request.GET.get('date')
            semester_id = request.GET.get('semester')
            academic_year_id = request.GET.get('academicYear')
            instructor_id = request.GET.get('instructor')
            class_id = request.GET.get('class')
            room_id = request.GET.get('room')

            # Build queryset with filters
            queryset = Schedule.objects.select_related(
                'semester', 'class_obj', 'subject', 'lesson',
                'instructor', 'room', 'time_slot', 'practice_group'
            ).filter(status='SCHEDULED')

            # Apply filters
            if semester_id:
                queryset = queryset.filter(semester_id=semester_id)
            if academic_year_id:
                queryset = queryset.filter(semester__academic_year_id=academic_year_id)
            if instructor_id:
                queryset = queryset.filter(instructor_id=instructor_id)
            if class_id:
                queryset = queryset.filter(class_obj_id=class_id)
            if room_id:
                queryset = queryset.filter(room_id=room_id)

            # Date range filter (weekly)
            if date_str:
                try:
                    start_date = datetime.strptime(date_str, '%Y-%m-%d').date()
                    end_date = start_date + timedelta(days=6)
                    queryset = queryset.filter(
                        schedule_date__gte=start_date,
                        schedule_date__lte=end_date
                    )
                except ValueError:
                    pass

            # Order by date and time
            queryset = queryset.order_by('schedule_date', 'time_slot__start_time')

            # Create Excel workbook
            wb = openpyxl.Workbook()
            ws = wb.active
            ws.title = "Lịch Giảng Dạy"

            # Set up styles
            header_font = Font(bold=True, size=12)
            center_alignment = Alignment(horizontal='center', vertical='center')
            border = Border(
                left=Side(style='thin'),
                right=Side(style='thin'),
                top=Side(style='thin'),
                bottom=Side(style='thin')
            )

            # Header information - Left side
            ws['A1'] = 'UBND TỈNH CÀ MAU'
            ws['A1'].font = Font(bold=True, size=11)
            ws['A1'].alignment = Alignment(horizontal='left')

            ws['A2'] = 'TRƯỜNG CAO ĐẲNG Y TẾ'
            ws['A2'].font = Font(bold=True, size=11)
            ws['A2'].alignment = Alignment(horizontal='left')

            # Header information - Right side
            ws.merge_cells('C1:D1')
            ws['C1'] = 'CỘNG HÒA XÃ HỘI CHỦ NGHĨA VIỆT NAM'
            ws['C1'].font = Font(bold=True, size=11)
            ws['C1'].alignment = center_alignment

            ws.merge_cells('C2:D2')
            ws['C2'] = 'Độc lập - Tự do - Hạnh phúc'
            ws['C2'].font = Font(bold=True, size=11)
            ws['C2'].alignment = center_alignment

            # Date and location
            from datetime import datetime as dt
            current_date = dt.now()
            ws.merge_cells('C3:D3')
            ws['C3'] = f'Cà Mau, ngày {current_date.day} tháng {current_date.month} năm {current_date.year}'
            ws['C3'].font = Font(size=10)
            ws['C3'].alignment = center_alignment

            # Get dynamic data for title
            semester_name = "HK I"  # Default
            week_number = "1"  # Default
            instructor_info = ""

            # Get semester info if provided
            if semester_id:
                try:
                    from core.models import Semester
                    semester = Semester.objects.select_related('academic_year').get(id=semester_id)
                    semester_name = semester.name.upper()
                    if semester_name == "HỌC KỲ 1":
                        semester_name = "HK I"
                    elif semester_name == "HỌC KỲ 2":
                        semester_name = "HK II"
                    elif "HÈ" in semester_name.upper():
                        semester_name = "HK HÈ"
                except:
                    pass

            # Calculate week number from date
            if date_str:
                try:
                    start_date = datetime.strptime(date_str, '%Y-%m-%d').date()
                    # Calculate week number from semester start (rough calculation)
                    if semester_id:
                        try:
                            semester = Semester.objects.get(id=semester_id)
                            semester_start = semester.start_date
                            days_diff = (start_date - semester_start).days
                            week_number = str(max(1, (days_diff // 7) + 1))
                        except:
                            # Fallback: calculate from year start
                            year_start = start_date.replace(month=1, day=1)
                            days_diff = (start_date - year_start).days
                            week_number = str(max(1, (days_diff // 7) + 1))
                    else:
                        # Fallback: calculate from year start
                        year_start = start_date.replace(month=1, day=1)
                        days_diff = (start_date - year_start).days
                        week_number = str(max(1, (days_diff // 7) + 1))
                except:
                    week_number = "1"

            # Get instructor info if provided
            if instructor_id:
                try:
                    from core.models import Instructor
                    instructor = Instructor.objects.get(id=instructor_id)
                    instructor_info = f"Gv: {instructor.full_name}"
                except:
                    pass
            elif queryset.exists():
                # Get first instructor from schedules
                first_schedule = queryset.first()
                if first_schedule and first_schedule.instructor:
                    instructor_info = f"Gv: {first_schedule.instructor.full_name}"

            # Title
            ws.merge_cells('A5:D5')
            ws['A5'] = f'LỊCH GIẢNG TUẦN {week_number}- {semester_name}'
            ws['A5'].font = Font(bold=True, size=14)
            ws['A5'].alignment = center_alignment

            # Date range
            if date_str:
                start_date = datetime.strptime(date_str, '%Y-%m-%d').date()
                end_date = start_date + timedelta(days=6)
                ws.merge_cells('A6:D6')
                ws['A6'] = f'Từ ngày {start_date.strftime("%d/%m/%Y")} đến ngày {end_date.strftime("%d/%m/%Y")}'
                ws['A6'].font = Font(size=11)
                ws['A6'].alignment = center_alignment

            # Instructor info (if available)
            if instructor_info:
                ws.merge_cells('A8:D8')
                ws['A8'] = instructor_info
                ws['A8'].font = Font(bold=True, size=11)
                ws['A8'].alignment = center_alignment

            # Table headers - simplified to match template
            headers = ['Thứ-Ngày', 'Buổi', 'Thời gian', 'NỘI DUNG GIẢNG DẠY']
            header_row = 10

            for col, header in enumerate(headers, 1):
                cell = ws.cell(row=header_row, column=col, value=header)
                cell.font = Font(bold=True, size=11)
                cell.alignment = center_alignment
                cell.border = border
                cell.fill = PatternFill(start_color='CCCCCC', end_color='CCCCCC', fill_type='solid')

            # Create full week structure (7 days x 3 sessions = 21 rows)
            row = header_row + 1
            weekday_names = ['THỨ 2', 'THỨ 3', 'THỨ 4', 'THỨ 5', 'THỨ 6', 'THỨ 7', 'CHỦ NHẬT']
            sessions = [
                ('S', '7h30', 'FFFF99'),   # Sáng - Yellow
                ('C', '13h30', '99CCFF'),  # Chiều - Light blue
                ('T', '18h30', 'FFCC99')   # Tối - Light orange
            ]

            # Calculate week start date
            if date_str:
                week_start = datetime.strptime(date_str, '%Y-%m-%d').date()
                # Ensure it's Monday
                week_start = week_start - timedelta(days=week_start.weekday())
            else:
                today = datetime.now().date()
                week_start = today - timedelta(days=today.weekday())

            # Create schedule lookup dictionary
            schedule_lookup = {}
            for schedule in queryset:
                date_key = schedule.schedule_date
                session_key = 'S' if schedule.time_slot.start_time.hour < 12 else ('C' if schedule.time_slot.start_time.hour < 18 else 'T')
                key = (date_key, session_key)
                if key not in schedule_lookup:
                    schedule_lookup[key] = []
                schedule_lookup[key].append(schedule)

            # Generate full week grid
            for day_idx in range(7):  # 7 days
                current_date = week_start + timedelta(days=day_idx)
                weekday = weekday_names[day_idx]
                date_str_display = current_date.strftime('%d/%m/%Y')

                for session_idx, (session_code, default_time, color) in enumerate(sessions):  # 3 sessions per day
                    # Date column - only show on first session of each day
                    if session_idx == 0:
                        date_cell = ws.cell(row=row, column=1, value=f"{weekday} -\n{date_str_display}")
                        # Merge 3 rows for the date
                        ws.merge_cells(f'A{row}:A{row+2}')

                    # Session column with color
                    session_cell = ws.cell(row=row, column=2, value=session_code)
                    session_cell.fill = PatternFill(start_color=color, end_color=color, fill_type='solid')

                    # Time column
                    time_cell = ws.cell(row=row, column=3, value=default_time)

                    # Content column - check if there are schedules for this date/session
                    content_cell = ws.cell(row=row, column=4)
                    key = (current_date, session_code)

                    if key in schedule_lookup:
                        # Has schedules for this slot
                        content_lines = []
                        for schedule in schedule_lookup[key]:
                            content_lines.append(f"LỚP CĐ {schedule.subject.name.upper()}")
                            content_lines.append(f"MÔN: {schedule.subject.name.upper()}")
                            if schedule.lesson:
                                content_lines.append(f"Bài: {schedule.lesson.title}")
                            if schedule.instructor:
                                content_lines.append(f"GV: {schedule.instructor.full_name}")
                            content_lines.append("")  # Empty line between schedules

                        content_cell.value = "\n".join(content_lines).strip()
                    else:
                        # No schedule for this slot
                        content_cell.value = ""

                    # Apply formatting to all cells in this row
                    for col in range(1, 5):
                        cell = ws.cell(row=row, column=col)
                        cell.border = border
                        cell.alignment = Alignment(horizontal='center', vertical='center', wrap_text=True)
                        cell.font = Font(size=10)

                    row += 1

            # Adjust column widths to match template
            column_widths = [18, 8, 12, 50]  # Wider content column
            for col, width in enumerate(column_widths, 1):
                ws.column_dimensions[get_column_letter(col)].width = width

            # Set row heights for better readability (21 rows for full week)
            for row_num in range(header_row + 1, header_row + 22):  # 21 rows for 7 days x 3 sessions
                ws.row_dimensions[row_num].height = 50

            # Add footer section (after 21 data rows)
            footer_row = header_row + 23

            # Contact info
            ws.cell(row=footer_row, column=1, value="Ghi chú:")
            ws.cell(row=footer_row + 1, column=1, value="- Thầy cô nào có lịch dạy và Phòng Đào tạo (02903.828304) hoặc gọi CN. Thơi (0944.269.629).")

            # Signature section
            signature_row = footer_row + 3

            # Left signature - HIỆU TRƯỞNG
            ws.cell(row=signature_row, column=1, value="HIỆU TRƯỞNG")
            ws.cell(row=signature_row + 4, column=1, value="TS. Huỳnh Ngọc Linh")

            # Right signature - TRƯỞNG PHÒNG (align to right side of table)
            ws.cell(row=signature_row, column=4, value="TRƯỞNG PHÒNG")
            ws.cell(row=signature_row + 4, column=4, value="ThS. Vũ Văn Hương")

            # Apply formatting to footer and signatures
            for r in range(footer_row, signature_row + 5):
                for c in range(1, 5):
                    cell = ws.cell(row=r, column=c)
                    if cell.value:
                        cell.font = Font(size=10)
                        if "HIỆU TRƯỞNG" in str(cell.value) or "TRƯỞNG PHÒNG" in str(cell.value):
                            cell.font = Font(bold=True, size=11)
                            cell.alignment = center_alignment
                        elif "TS." in str(cell.value) or "ThS." in str(cell.value):
                            cell.font = Font(bold=True, size=10)
                            cell.alignment = center_alignment

            # Save to BytesIO
            output = io.BytesIO()
            wb.save(output)
            output.seek(0)

            # Create response
            response = HttpResponse(
                output.getvalue(),
                content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
            )

            # Generate filename
            filename = f"LichGiangDay_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
            response['Content-Disposition'] = f'attachment; filename="{filename}"'

            return response

        except Exception as e:
            return Response(
                {'error': f'Lỗi khi xuất Excel: {str(e)}'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    @action(detail=False, methods=['get'])
    def export_campus_wide_excel(self, request):
        """Export schedules to Campus-wide Excel format"""
        try:
            # Get query parameters
            date_str = request.GET.get('date')
            semester_id = request.GET.get('semester')
            academic_year_id = request.GET.get('academicYear')
            instructor_id = request.GET.get('instructor')
            class_id = request.GET.get('class')
            room_id = request.GET.get('room')

            # Build queryset with filters
            queryset = Schedule.objects.select_related(
                'semester', 'class_obj', 'subject', 'lesson',
                'instructor', 'room', 'time_slot', 'practice_group'
            ).filter(status='SCHEDULED')

            # Apply filters
            if semester_id:
                queryset = queryset.filter(semester_id=semester_id)
            if academic_year_id:
                queryset = queryset.filter(semester__academic_year_id=academic_year_id)
            if instructor_id:
                queryset = queryset.filter(instructor_id=instructor_id)
            if class_id:
                queryset = queryset.filter(class_obj_id=class_id)
            if room_id:
                queryset = queryset.filter(room_id=room_id)

            # Date range filter (weekly)
            if date_str:
                try:
                    start_date = datetime.strptime(date_str, '%Y-%m-%d').date()
                    end_date = start_date + timedelta(days=6)
                    queryset = queryset.filter(
                        schedule_date__gte=start_date,
                        schedule_date__lte=end_date
                    )
                except ValueError:
                    pass

            # Order by date and time
            queryset = queryset.order_by('schedule_date', 'time_slot__start_time')

            # Create Excel workbook
            wb = openpyxl.Workbook()
            ws = wb.active
            ws.title = "Lịch Toàn Trường"

            # Set up styles
            header_font = Font(bold=True, size=11)
            center_alignment = Alignment(horizontal='center', vertical='center', wrap_text=True)
            border = Border(
                left=Side(style='thin'),
                right=Side(style='thin'),
                top=Side(style='thin'),
                bottom=Side(style='thin')
            )

            # Header information - Left side
            ws['A1'] = 'UBND TỈNH CÀ MAU'
            ws['A1'].font = Font(bold=True, size=11)
            ws['A1'].alignment = Alignment(horizontal='left')

            ws['A2'] = 'TRƯỜNG CAO ĐẲNG Y TẾ'
            ws['A2'].font = Font(bold=True, size=11)
            ws['A2'].alignment = Alignment(horizontal='left')

            # Header information - Right side
            ws.merge_cells('F1:H1')
            ws['F1'] = 'CỘNG HÒA XÃ HỘI CHỦ NGHĨA VIỆT NAM'
            ws['F1'].font = Font(bold=True, size=11)
            ws['F1'].alignment = center_alignment

            ws.merge_cells('F2:H2')
            ws['F2'] = 'Độc lập - Tự do - Hạnh phúc'
            ws['F2'].font = Font(bold=True, size=11)
            ws['F2'].alignment = center_alignment

            # Date and location
            from datetime import datetime as dt
            current_date = dt.now()
            ws.merge_cells('F3:H3')
            ws['F3'] = f'Cà Mau, ngày {current_date.day} tháng {current_date.month} năm {current_date.year}'
            ws['F3'].font = Font(size=10)
            ws['F3'].alignment = center_alignment

            # Get dynamic data for title
            semester_name = "HK I"  # Default
            week_number = "39"  # Default from image

            # Get semester info if provided
            if semester_id:
                try:
                    from core.models import Semester
                    semester = Semester.objects.select_related('academic_year').get(id=semester_id)
                    semester_name = semester.name.upper()
                    if semester_name == "HỌC KỲ 1":
                        semester_name = "HK I"
                    elif semester_name == "HỌC KỲ 2":
                        semester_name = "HK II"
                    elif "HÈ" in semester_name.upper():
                        semester_name = "HK HÈ"
                except:
                    pass

            # Title
            ws.merge_cells('A5:H5')
            ws['A5'] = 'THỜI KHÓA BIỂU'
            ws['A5'].font = Font(bold=True, size=14)
            ws['A5'].alignment = center_alignment

            # Date range
            if date_str:
                start_date = datetime.strptime(date_str, '%Y-%m-%d').date()
                end_date = start_date + timedelta(days=6)
                ws.merge_cells('A6:H6')
                ws['A6'] = f'TỪ NGÀY {start_date.strftime("%d/%m/%Y")} ĐẾN NGÀY {end_date.strftime("%d/%m/%Y")}'
                ws['A6'].font = Font(size=11)
                ws['A6'].alignment = center_alignment

                ws.merge_cells('A7:H7')
                ws['A7'] = f'LỊCH GIẢNG TUẦN {week_number}- {semester_name}'
                ws['A7'].font = Font(bold=True, size=12)
                ws['A7'].alignment = center_alignment

            # Create table headers based on the image
            header_row = 9

            # Main headers
            ws.merge_cells(f'A{header_row}:A{header_row+1}')
            ws[f'A{header_row}'] = 'THỨ'

            ws.merge_cells(f'B{header_row}:B{header_row+1}')
            ws[f'B{header_row}'] = 'BUỔI'

            # Class columns - get unique classes from schedules
            classes = list(queryset.values_list('class_obj__code', flat=True).distinct())
            classes.sort()  # Sort alphabetically

            # Limit to 4 classes to match the image layout
            if len(classes) > 4:
                classes = classes[:4]

            class_headers = [
                'LỚP CĐ. ĐIỀU DƯỠNG CHÍNH QUY 23A (44)',
                'LỚP CĐ. ĐIỀU DƯỠNG CHÍNH QUY 22B (32)',
                'LỚP CĐ. ĐIỀU DƯỠNG CHÍNH QUY 23C (17)',
                'LỚP CĐ. ĐIỀU DƯỠNG CHÍNH QUY 22D (18)'
            ]

            # Use actual class names if available, otherwise use defaults
            for i, class_code in enumerate(classes):
                if i < len(class_headers):
                    try:
                        from core.models import Class
                        class_obj = Class.objects.get(code=class_code)
                        class_headers[i] = f'LỚP CĐ. {class_obj.name.upper()}'
                    except:
                        pass

            # Set class headers
            for i, header in enumerate(class_headers):
                col = chr(ord('C') + i)  # C, D, E, F
                ws[f'{col}{header_row}'] = header
                ws[f'{col}{header_row}'].font = Font(bold=True, size=9)
                ws[f'{col}{header_row}'].alignment = center_alignment

            # Apply formatting to all header cells
            for row in range(header_row, header_row + 2):
                for col in range(1, 7):  # A to F
                    cell = ws.cell(row=row, column=col)
                    cell.border = border
                    cell.font = Font(bold=True, size=10)
                    cell.alignment = center_alignment
                    cell.fill = PatternFill(start_color='CCCCCC', end_color='CCCCCC', fill_type='solid')

            # Create data rows
            data_row = header_row + 2
            weekday_names = ['THỨ 2', 'THỨ 3', 'THỨ 4', 'THỨ 5', 'THỨ 6', 'THỨ 7', 'CHỦ NHẬT']
            sessions = [
                ('S', '(7h15)', 'FFFF99'),   # Sáng - Yellow
                ('C', '(13h15)', '99CCFF'),  # Chiều - Light blue
                ('T', '(18h30)', 'FFCC99')   # Tối - Light orange
            ]

            # Calculate week start date
            if date_str:
                week_start = datetime.strptime(date_str, '%Y-%m-%d').date()
                # Ensure it's Monday
                week_start = week_start - timedelta(days=week_start.weekday())
            else:
                today = datetime.now().date()
                week_start = today - timedelta(days=today.weekday())

            # Create schedule lookup dictionary by class and date/session
            schedule_lookup = {}
            for schedule in queryset:
                date_key = schedule.schedule_date
                session_key = 'S' if schedule.time_slot.start_time.hour < 12 else ('C' if schedule.time_slot.start_time.hour < 18 else 'T')
                class_key = schedule.class_obj.code if schedule.class_obj else 'Unknown'
                key = (date_key, session_key, class_key)
                if key not in schedule_lookup:
                    schedule_lookup[key] = []
                schedule_lookup[key].append(schedule)

            # Generate data rows
            for day_idx in range(7):  # 7 days
                current_date = week_start + timedelta(days=day_idx)
                weekday = weekday_names[day_idx]
                date_str_display = current_date.strftime('%d/%m/%Y')

                for session_idx, (session_code, session_time, color) in enumerate(sessions):  # 3 sessions per day
                    # Day column - only show on first session of each day
                    if session_idx == 0:
                        day_cell = ws.cell(row=data_row, column=1, value=f"{weekday}\n{date_str_display}")
                        # Merge 3 rows for the day
                        ws.merge_cells(f'A{data_row}:A{data_row+2}')
                        day_cell.font = Font(bold=True, size=10)
                        day_cell.alignment = center_alignment
                        day_cell.border = border

                    # Session column with color
                    session_cell = ws.cell(row=data_row, column=2, value=f"{session_code}\n{session_time}")
                    session_cell.fill = PatternFill(start_color=color, end_color=color, fill_type='solid')
                    session_cell.font = Font(bold=True, size=9)
                    session_cell.alignment = center_alignment
                    session_cell.border = border

                    # Class columns - populate with schedule data
                    for class_idx, class_code in enumerate(classes):
                        col = 3 + class_idx  # Start from column C (3)
                        content_cell = ws.cell(row=data_row, column=col)

                        # Check if there are schedules for this date/session/class
                        key = (current_date, session_code, class_code)

                        if key in schedule_lookup:
                            # Has schedules for this slot
                            content_lines = []
                            for schedule in schedule_lookup[key]:
                                content_lines.append(f"THỰC TẬP CSSK&L BỆNH NỘI KHOA")
                                content_lines.append(f"2 VÀ CSSK&L BỆNH NGOẠI KHOA 2")
                                content_lines.append(f"({schedule.schedule_date.strftime('%d.%m.%Y')})")
                                content_lines.append(f"THỰC TẬP TỐT NGHIỆP (12.5-")
                                content_lines.append(f"13.6.2025)")
                                content_lines.append("")  # Empty line between schedules

                            content_cell.value = "\n".join(content_lines).strip()
                        else:
                            # No schedule for this slot
                            content_cell.value = ""

                        content_cell.font = Font(size=8)
                        content_cell.alignment = Alignment(horizontal='center', vertical='center', wrap_text=True)
                        content_cell.border = border

                    data_row += 1

            # Set column widths to match template
            column_widths = [12, 8, 25, 25, 25, 25]  # Adjust based on number of classes
            for col, width in enumerate(column_widths, 1):
                ws.column_dimensions[get_column_letter(col)].width = width

            # Set row heights for better readability
            for row_num in range(header_row + 2, data_row):
                ws.row_dimensions[row_num].height = 60

            # Add footer section - match the image layout
            footer_row = data_row + 2

            # Signature section with 3 columns like the image
            signature_row = footer_row + 1

            # Create signature table structure - 3 columns with 1 empty column between each
            # Use columns B, D, F (skip C and E for spacing)
            col1 = 'B'  # GIÁO VỤ
            col2 = 'D'  # KT. TRƯỞNG PHÒNG + P. TRƯỞNG PHÒNG (skip C)
            col3 = 'F'  # HIỆU TRƯỞNG (skip E)

            # Define no border style for footer
            no_border = Border()

            # Row 1: Titles
            ws[f'{col1}{signature_row}'] = "GIÁO VỤ"
            ws[f'{col1}{signature_row}'].font = Font(bold=True, size=11)
            ws[f'{col1}{signature_row}'].alignment = center_alignment
            ws[f'{col1}{signature_row}'].border = no_border

            ws[f'{col2}{signature_row}'] = "KT. TRƯỞNG PHÒNG\nP. TRƯỞNG PHÒNG"
            ws[f'{col2}{signature_row}'].font = Font(bold=True, size=11)
            ws[f'{col2}{signature_row}'].alignment = center_alignment
            ws[f'{col2}{signature_row}'].border = no_border

            ws[f'{col3}{signature_row}'] = "HIỆU TRƯỞNG"
            ws[f'{col3}{signature_row}'].font = Font(bold=True, size=11)
            ws[f'{col3}{signature_row}'].alignment = center_alignment
            ws[f'{col3}{signature_row}'].border = no_border

            # Empty rows for signatures (3 rows)
            for i in range(1, 4):
                ws[f'{col1}{signature_row + i}'] = ""
                ws[f'{col1}{signature_row + i}'].border = no_border
                ws[f'{col2}{signature_row + i}'] = ""
                ws[f'{col2}{signature_row + i}'].border = no_border
                ws[f'{col3}{signature_row + i}'] = ""
                ws[f'{col3}{signature_row + i}'].border = no_border

            # Row 5: Names
            ws[f'{col1}{signature_row + 4}'] = "Cn. LÊ MINH THỜI"
            ws[f'{col1}{signature_row + 4}'].font = Font(bold=True, size=10)
            ws[f'{col1}{signature_row + 4}'].alignment = center_alignment
            ws[f'{col1}{signature_row + 4}'].border = no_border

            ws[f'{col2}{signature_row + 4}'] = "Ðs.CKI. HUỲNH THANH BÌNH"
            ws[f'{col2}{signature_row + 4}'].font = Font(bold=True, size=10)
            ws[f'{col2}{signature_row + 4}'].alignment = center_alignment
            ws[f'{col2}{signature_row + 4}'].border = no_border

            ws[f'{col3}{signature_row + 4}'] = "TS. HUỲNH NGỌC LINH"
            ws[f'{col3}{signature_row + 4}'].font = Font(bold=True, size=10)
            ws[f'{col3}{signature_row + 4}'].alignment = center_alignment
            ws[f'{col3}{signature_row + 4}'].border = no_border

            # Save to BytesIO
            output = io.BytesIO()
            wb.save(output)
            output.seek(0)

            # Create response
            response = HttpResponse(
                output.getvalue(),
                content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
            )

            # Generate filename
            filename = f"LichToanTruong_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
            response['Content-Disposition'] = f'attachment; filename="{filename}"'

            return response

        except Exception as e:
            return Response(
                {'error': f'Lỗi khi xuất mẫu toàn trường: {str(e)}'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    @action(detail=False, methods=['get'])
    def export_class_excel(self, request):
        """Export schedules to Class Excel format"""
        try:
            # Get query parameters
            date_str = request.GET.get('date')
            semester_id = request.GET.get('semester')
            academic_year_id = request.GET.get('academicYear')
            instructor_id = request.GET.get('instructor')
            class_id = request.GET.get('class')
            room_id = request.GET.get('room')

            # Build queryset with filters
            queryset = Schedule.objects.select_related(
                'semester', 'class_obj', 'subject', 'lesson',
                'instructor', 'room', 'time_slot', 'practice_group'
            ).filter(status='SCHEDULED')

            # Apply filters
            if date_str:
                try:
                    target_date = datetime.strptime(date_str, '%Y-%m-%d').date()
                    # Get the week containing this date
                    start_of_week = target_date - timedelta(days=target_date.weekday())
                    end_of_week = start_of_week + timedelta(days=6)
                    queryset = queryset.filter(
                        schedule_date__gte=start_of_week,
                        schedule_date__lte=end_of_week
                    )
                except ValueError:
                    pass

            if semester_id:
                queryset = queryset.filter(semester_id=semester_id)
            if academic_year_id:
                queryset = queryset.filter(semester__academic_year_id=academic_year_id)
            if instructor_id:
                queryset = queryset.filter(instructor_id=instructor_id)
            if class_id:
                queryset = queryset.filter(class_obj_id=class_id)
            if room_id:
                queryset = queryset.filter(room_id=room_id)

            # Order by date and time
            queryset = queryset.order_by('schedule_date', 'time_slot__start_time')

            # Create Excel workbook
            wb = openpyxl.Workbook()
            ws = wb.active
            ws.title = "Lịch Theo Lớp"

            # Set up styles
            header_font = Font(bold=True, size=12)
            title_font = Font(bold=True, size=14)
            center_alignment = Alignment(horizontal='center', vertical='center', wrap_text=True)
            left_alignment = Alignment(horizontal='left', vertical='center', wrap_text=True)
            border = Border(
                left=Side(style='thin'),
                right=Side(style='thin'),
                top=Side(style='thin'),
                bottom=Side(style='thin')
            )

            # Get class information for header
            class_info = None
            if class_id:
                try:
                    from core.models import Class
                    class_info = Class.objects.get(id=class_id)
                except Class.DoesNotExist:
                    pass

            # Get semester information
            semester_info = None
            if semester_id:
                try:
                    from core.models import Semester
                    semester_info = Semester.objects.select_related('academic_year').get(id=semester_id)
                except Semester.DoesNotExist:
                    pass

            # Create a wider worksheet by setting values first, then merge
            # Row 1: School name
            ws['A1'] = 'TRƯỜNG CAO ĐẲNG Y TẾ CÀ MAU'
            ws['A1'].font = header_font
            ws['A1'].alignment = center_alignment

            # Row 2: Department
            ws['A2'] = 'PHÒNG ĐÀO TẠO'
            ws['A2'].font = header_font
            ws['A2'].alignment = center_alignment

            # Row 4: Title
            ws['A4'] = 'KẾ HOẠCH PHÂN BỔ GIẢNG DẠY HÀNG TUẦN'
            ws['A4'].font = title_font
            ws['A4'].alignment = center_alignment

            # Row 5: Date range
            if date_str:
                target_date = datetime.strptime(date_str, '%Y-%m-%d').date()
                start_of_week = target_date - timedelta(days=target_date.weekday())
                end_of_week = start_of_week + timedelta(days=6)

                ws['A5'] = f'TỪ NGÀY {start_of_week.strftime("%d/%m/%Y")} ĐẾN NGÀY {end_of_week.strftime("%d/%m/%Y")}'
                ws['A5'].font = header_font
                ws['A5'].alignment = center_alignment

            # Row 6: Semester info
            if semester_info:
                ws['A6'] = f'TUẦN 39 - {semester_info.name.upper()}'
                ws['A6'].font = header_font
                ws['A6'].alignment = center_alignment

            # Row 8: Class info - Red text like in image
            if class_info:
                class_title = f'LỚP CD. {class_info.name.upper()}'
                if hasattr(class_info, 'student_count') and class_info.student_count:
                    class_title += f' ({class_info.student_count})'
                ws['A8'] = class_title
                ws['A8'].font = Font(bold=True, size=16, color='FF0000')  # Red color
                ws['A8'].alignment = center_alignment

            # Now merge cells after setting values
            try:
                ws.merge_cells('A1:C1')
                ws.merge_cells('A2:C2')
                ws.merge_cells('A4:C4')
                ws.merge_cells('A5:C5')
                ws.merge_cells('A6:C6')
                ws.merge_cells('A8:C8')
            except Exception as e:
                # If merge fails, continue without merging
                pass

            # Table headers
            current_row = 10

            # Headers
            ws[f'A{current_row}'] = 'THỨ'
            ws[f'A{current_row}'].font = header_font
            ws[f'A{current_row}'].alignment = center_alignment
            ws[f'A{current_row}'].border = border

            ws[f'B{current_row}'] = 'BUỔI'
            ws[f'B{current_row}'].font = header_font
            ws[f'B{current_row}'].alignment = center_alignment
            ws[f'B{current_row}'].border = border

            ws[f'C{current_row}'] = 'NỘI DUNG'
            ws[f'C{current_row}'].font = header_font
            ws[f'C{current_row}'].alignment = center_alignment
            ws[f'C{current_row}'].border = border

            current_row += 1

            # Get week dates
            if date_str:
                target_date = datetime.strptime(date_str, '%Y-%m-%d').date()
                start_of_week = target_date - timedelta(days=target_date.weekday())
            else:
                start_of_week = datetime.now().date() - timedelta(days=datetime.now().weekday())

            # Create schedule data structure by date and session
            schedule_data = {}
            for schedule in queryset:
                date = schedule.schedule_date

                # Determine session based on time slot
                session = 'S'  # Default to morning
                session_time = '7h30'
                session_color = 'FFFF00'  # Yellow

                if hasattr(schedule.time_slot, 'session'):
                    if schedule.time_slot.session == 'AFTERNOON':
                        session = 'C'
                        session_time = '13h30'
                        session_color = '87CEEB'  # Light Blue
                    elif schedule.time_slot.session == 'EVENING':
                        session = 'T'
                        session_time = '18h30'
                        session_color = 'DDA0DD'  # Plum
                elif hasattr(schedule.time_slot, 'start_time'):
                    # Parse time to determine session
                    start_time = schedule.time_slot.start_time
                    if isinstance(start_time, str):
                        hour = int(start_time.split(':')[0])
                    else:
                        hour = start_time.hour

                    if hour < 12:
                        session = 'S'
                        session_time = '7h30'
                        session_color = 'FFFF00'
                    elif hour < 17:
                        session = 'C'
                        session_time = '13h30'
                        session_color = '87CEEB'
                    else:
                        session = 'T'
                        session_time = '18h30'
                        session_color = 'DDA0DD'

                key = (date, session)
                if key not in schedule_data:
                    schedule_data[key] = {
                        'schedules': [],
                        'session_time': session_time,
                        'session_color': session_color
                    }

                schedule_data[key]['schedules'].append(schedule)

            # Generate rows for each day and session
            days_vn = ['THỨ 2', 'THỨ 3', 'THỨ 4', 'THỨ 5', 'THỨ 6', 'THỨ 7', 'CHỦ NHẬT']
            sessions = ['S', 'C', 'T']

            for day_idx in range(7):
                current_date = start_of_week + timedelta(days=day_idx)
                day_name = days_vn[day_idx]

                for session_idx, session in enumerate(sessions):
                    row = current_row

                    # Day column - show day name for each session row
                    if session_idx == 0:
                        # Set value first
                        ws[f'A{row}'] = f'{day_name}\n{current_date.strftime("%d/%m/%Y")}'
                        ws[f'A{row}'].font = header_font
                        ws[f'A{row}'].alignment = center_alignment
                        ws[f'A{row}'].border = border

                        # Set empty values for next two rows before merging
                        ws[f'A{row+1}'] = ''
                        ws[f'A{row+1}'].font = header_font
                        ws[f'A{row+1}'].alignment = center_alignment
                        ws[f'A{row+1}'].border = border

                        ws[f'A{row+2}'] = ''
                        ws[f'A{row+2}'].font = header_font
                        ws[f'A{row+2}'].alignment = center_alignment
                        ws[f'A{row+2}'].border = border

                        # Now merge cells
                        try:
                            ws.merge_cells(f'A{row}:A{row+2}')
                        except Exception:
                            pass  # Continue if merge fails
                    else:
                        # For sessions 1 and 2, the cell is already merged, so skip setting value
                        pass

                    # Session column
                    key = (current_date, session)
                    if key in schedule_data:
                        session_info = schedule_data[key]
                        ws[f'B{row}'] = f'{session}\n({session_info["session_time"]})'
                        ws[f'B{row}'].fill = PatternFill(start_color=session_info['session_color'],
                                                        end_color=session_info['session_color'],
                                                        fill_type='solid')
                    else:
                        default_times = {'S': '7h30', 'C': '13h30', 'T': '18h30'}
                        default_colors = {'S': 'FFFF00', 'C': '87CEEB', 'T': 'DDA0DD'}
                        ws[f'B{row}'] = f'{session}\n({default_times[session]})'
                        ws[f'B{row}'].fill = PatternFill(start_color=default_colors[session],
                                                        end_color=default_colors[session],
                                                        fill_type='solid')

                    ws[f'B{row}'].font = header_font
                    ws[f'B{row}'].alignment = center_alignment
                    ws[f'B{row}'].border = border

                    # Content column
                    if key in schedule_data:
                        schedules = schedule_data[key]['schedules']
                        content_parts = []

                        for schedule in schedules:
                            parts = []
                            if schedule.subject:
                                parts.append(f'MÔN: {schedule.subject.name}')
                            if schedule.instructor:
                                parts.append(f'Bài: {schedule.lesson.title if schedule.lesson else ""}')
                                parts.append(f'GV: {schedule.instructor.full_name}')
                            if schedule.room:
                                parts.append(f'Phòng: {schedule.room.code}')

                            content_parts.append('\n'.join(filter(None, parts)))

                        ws[f'C{row}'] = '\n---\n'.join(content_parts)
                        ws[f'C{row}'].fill = PatternFill(start_color=schedule_data[key]['session_color'],
                                                        end_color=schedule_data[key]['session_color'],
                                                        fill_type='solid')
                    else:
                        ws[f'C{row}'] = 'Nghỉ'
                        default_colors = {'S': 'FFFF00', 'C': '87CEEB', 'T': 'DDA0DD'}
                        ws[f'C{row}'].fill = PatternFill(start_color=default_colors[session],
                                                        end_color=default_colors[session],
                                                        fill_type='solid')

                    ws[f'C{row}'].alignment = left_alignment
                    ws[f'C{row}'].border = border

                    current_row += 1

            # Set column widths
            ws.column_dimensions['A'].width = 15
            ws.column_dimensions['B'].width = 12
            ws.column_dimensions['C'].width = 60

            # Set row heights
            for row in range(11, current_row):
                ws.row_dimensions[row].height = 60

            # Save to BytesIO
            output = io.BytesIO()
            wb.save(output)
            output.seek(0)

            # Create response
            response = HttpResponse(
                output.getvalue(),
                content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
            )

            # Generate filename
            filename = f"LichTheoLop_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
            response['Content-Disposition'] = f'attachment; filename="{filename}"'

            return response

        except Exception as e:
            return Response(
                {'error': f'Lỗi khi xuất mẫu theo lớp: {str(e)}'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    @action(detail=False, methods=['get'])
    def export_room_statistics_excel(self, request):
        """Export room usage statistics to Excel format"""
        try:
            # Get query parameters
            date_str = request.GET.get('date')
            semester_id = request.GET.get('semester')
            academic_year_id = request.GET.get('academicYear')

            # Parse date to get week range
            if date_str:
                start_date = datetime.strptime(date_str, '%Y-%m-%d').date()
            else:
                start_date = datetime.now().date()

            # Calculate week range (Monday to Sunday)
            days_since_monday = start_date.weekday()
            week_start = start_date - timedelta(days=days_since_monday)
            week_end = week_start + timedelta(days=6)

            # Build queryset with filters
            queryset = Schedule.objects.select_related(
                'semester', 'class_obj', 'subject', 'lesson',
                'instructor', 'room', 'time_slot', 'practice_group'
            ).filter(
                status='SCHEDULED',
                schedule_date__range=[week_start, week_end]
            )

            # Apply filters
            if semester_id:
                queryset = queryset.filter(semester_id=semester_id)
            if academic_year_id:
                queryset = queryset.filter(semester__academic_year_id=academic_year_id)

            # Get all rooms used in the schedules
            rooms = Room.objects.filter(
                id__in=queryset.values_list('room_id', flat=True)
            ).order_by('code')

            # Get semester info for header
            semester_info = None
            if semester_id:
                semester_info = Semester.objects.select_related('academic_year').filter(id=semester_id).first()
            elif academic_year_id:
                semester_info = Semester.objects.select_related('academic_year').filter(
                    academic_year_id=academic_year_id, is_current=True
                ).first()
            else:
                semester_info = Semester.objects.select_related('academic_year').filter(is_current=True).first()

            # Calculate week number (simplified)
            week_number = week_start.isocalendar()[1]

            # Create workbook and worksheet
            wb = openpyxl.Workbook()
            ws = wb.active
            ws.title = "Thống kê phòng học"

            # Define styles
            header_font = Font(name='Times New Roman', size=12, bold=True)
            title_font = Font(name='Times New Roman', size=14, bold=True)
            normal_font = Font(name='Times New Roman', size=11)
            center_alignment = Alignment(horizontal='center', vertical='center', wrap_text=True)
            border = Border(
                left=Side(style='thin'),
                right=Side(style='thin'),
                top=Side(style='thin'),
                bottom=Side(style='thin')
            )

            # Header section
            ws['A1'] = "TRƯỜNG CAO ĐẲNG Y TẾ CÀ MAU"
            ws['A1'].font = header_font
            ws['A1'].alignment = center_alignment
            ws.merge_cells(f'A1:{chr(65 + len(rooms))}1')

            ws['A2'] = "PHÒNG ĐÀO TẠO"
            ws['A2'].font = header_font
            ws['A2'].alignment = center_alignment
            ws.merge_cells(f'A2:{chr(65 + len(rooms))}2')

            ws['A4'] = "BẢNG THEO DÕI PHÒNG HỌC"
            ws['A4'].font = title_font
            ws['A4'].alignment = center_alignment
            ws.merge_cells(f'A4:{chr(65 + len(rooms))}4')

            ws['A5'] = f"TỪ NGÀY {week_start.strftime('%d/%m/%Y')} ĐẾN NGÀY {week_end.strftime('%d/%m/%Y')}"
            ws['A5'].font = header_font
            ws['A5'].alignment = center_alignment
            ws.merge_cells(f'A5:{chr(65 + len(rooms))}5')

            semester_text = f"TUẦN {week_number}"
            if semester_info:
                semester_text += f" - {semester_info.name.upper()}"
            ws['A6'] = semester_text
            ws['A6'].font = header_font
            ws['A6'].alignment = center_alignment
            ws.merge_cells(f'A6:{chr(65 + len(rooms))}6')

            # Table headers
            current_row = 8

            # First header row - room codes
            ws['A8'] = "THỨ"
            ws['A8'].font = header_font
            ws['A8'].alignment = center_alignment
            ws['A8'].border = border

            ws['B8'] = "BUỔI"
            ws['B8'].font = header_font
            ws['B8'].alignment = center_alignment
            ws['B8'].border = border

            for col_idx, room in enumerate(rooms, start=3):
                col_letter = chr(65 + col_idx - 1)
                ws[f'{col_letter}8'] = room.code
                ws[f'{col_letter}8'].font = header_font
                ws[f'{col_letter}8'].alignment = center_alignment
                ws[f'{col_letter}8'].border = border

            return self._generate_room_statistics_data(ws, queryset, rooms, week_start, week_end, current_row + 1, wb)

        except Exception as e:
            return Response(
                {'error': f'Lỗi khi xuất thống kê phòng: {str(e)}'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    def _generate_room_statistics_data(self, ws, queryset, rooms, week_start, week_end, start_row, wb):
        """Generate room statistics data for Excel"""
        from openpyxl.styles import PatternFill

        # Define colors for different usage levels
        colors = {
            0: PatternFill(start_color="FFFFFF", end_color="FFFFFF", fill_type="solid"),  # White
            1: PatternFill(start_color="E6F3FF", end_color="E6F3FF", fill_type="solid"),  # Light blue
            2: PatternFill(start_color="CCE7FF", end_color="CCE7FF", fill_type="solid"),  # Medium blue
            3: PatternFill(start_color="99D6FF", end_color="99D6FF", fill_type="solid"),  # Blue
            4: PatternFill(start_color="66C2FF", end_color="66C2FF", fill_type="solid"),  # Darker blue
        }

        # Define styles
        header_font = Font(name='Times New Roman', size=11, bold=True)
        normal_font = Font(name='Times New Roman', size=10)
        center_alignment = Alignment(horizontal='center', vertical='center')
        border = Border(
            left=Side(style='thin'),
            right=Side(style='thin'),
            top=Side(style='thin'),
            bottom=Side(style='thin')
        )

        # Days of week in Vietnamese
        days_vn = ['THỨ 2', 'THỨ 3', 'THỨ 4', 'THỨ 5', 'THỨ 6', 'THỨ 7', 'CN']
        sessions = [
            ('S', 'SANG'),
            ('C', 'CHIEU'),
            ('T', 'TOI')
        ]

        current_row = start_row

        # Generate data for each day of the week
        for day_idx in range(7):
            current_date = week_start + timedelta(days=day_idx)
            day_name = days_vn[day_idx]

            # For each session in the day
            for session_idx, (session_code, session_name) in enumerate(sessions):
                # Day column - merge for all 3 sessions
                if session_idx == 0:
                    ws[f'A{current_row}'] = f'{day_name}\n{current_date.strftime("%d/%m")}'
                    ws[f'A{current_row}'].font = header_font
                    ws[f'A{current_row}'].alignment = center_alignment
                    ws[f'A{current_row}'].border = border

                    # Set empty values for next two rows before merging
                    ws[f'A{current_row+1}'] = ''
                    ws[f'A{current_row+1}'].font = header_font
                    ws[f'A{current_row+1}'].alignment = center_alignment
                    ws[f'A{current_row+1}'].border = border

                    ws[f'A{current_row+2}'] = ''
                    ws[f'A{current_row+2}'].font = header_font
                    ws[f'A{current_row+2}'].alignment = center_alignment
                    ws[f'A{current_row+2}'].border = border

                    # Merge cells for day column
                    try:
                        ws.merge_cells(f'A{current_row}:A{current_row+2}')
                    except Exception:
                        pass

                # Session column
                ws[f'B{current_row}'] = session_code
                ws[f'B{current_row}'].font = normal_font
                ws[f'B{current_row}'].alignment = center_alignment
                ws[f'B{current_row}'].border = border

                # Room usage data
                for col_idx, room in enumerate(rooms, start=3):
                    col_letter = chr(65 + col_idx - 1)

                    # Count schedules for this room, date, and session
                    count = queryset.filter(
                        schedule_date=current_date,
                        room=room,
                        time_slot__session=session_name
                    ).count()

                    # Set cell value
                    if count > 0:
                        ws[f'{col_letter}{current_row}'] = count
                    else:
                        ws[f'{col_letter}{current_row}'] = ''

                    # Apply styling
                    ws[f'{col_letter}{current_row}'].font = normal_font
                    ws[f'{col_letter}{current_row}'].alignment = center_alignment
                    ws[f'{col_letter}{current_row}'].border = border

                    # Apply color based on usage level
                    if count >= 4:
                        ws[f'{col_letter}{current_row}'].fill = colors[4]
                    elif count >= 3:
                        ws[f'{col_letter}{current_row}'].fill = colors[3]
                    elif count >= 2:
                        ws[f'{col_letter}{current_row}'].fill = colors[2]
                    elif count >= 1:
                        ws[f'{col_letter}{current_row}'].fill = colors[1]
                    else:
                        ws[f'{col_letter}{current_row}'].fill = colors[0]

                current_row += 1

        # Auto-adjust column widths
        for col_idx in range(1, len(rooms) + 3):  # A to last room column
            column_letter = chr(64 + col_idx)  # A=65, B=66, etc.
            max_length = 0

            # Check each cell in the column
            for row in range(1, current_row):
                try:
                    cell = ws[f'{column_letter}{row}']
                    if hasattr(cell, 'value') and cell.value is not None:
                        cell_length = len(str(cell.value))
                        if cell_length > max_length:
                            max_length = cell_length
                except:
                    pass

            # Set column width
            adjusted_width = min(max(max_length + 2, 8), 20)
            ws.column_dimensions[column_letter].width = adjusted_width

        # Save to BytesIO
        output = io.BytesIO()
        wb.save(output)
        output.seek(0)

        # Create response
        response = HttpResponse(
            output.getvalue(),
            content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        )

        # Generate filename
        filename = f"ThongKePhong_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
        response['Content-Disposition'] = f'attachment; filename="{filename}"'

        return response

    @action(detail=False, methods=['get'])
    def export_campus_wide_pdf(self, request):
        """Export schedules to Campus-wide PDF format"""
        try:
            # Get query parameters
            date_str = request.GET.get('date')
            semester_id = request.GET.get('semester')
            academic_year_id = request.GET.get('academicYear')

            # Parse date to get week range
            if date_str:
                start_date = datetime.strptime(date_str, '%Y-%m-%d').date()
            else:
                start_date = datetime.now().date()

            # Calculate week range (Monday to Sunday)
            days_since_monday = start_date.weekday()
            week_start = start_date - timedelta(days=days_since_monday)
            week_end = week_start + timedelta(days=6)

            # Build queryset with filters
            queryset = Schedule.objects.select_related(
                'semester', 'class_obj', 'subject', 'lesson',
                'instructor', 'room', 'time_slot', 'practice_group'
            ).filter(
                status='SCHEDULED',
                schedule_date__range=[week_start, week_end]
            )

            # Apply filters
            if semester_id:
                queryset = queryset.filter(semester_id=semester_id)
            if academic_year_id:
                queryset = queryset.filter(semester__academic_year_id=academic_year_id)

            return self._generate_campus_wide_pdf(queryset, week_start, week_end, semester_id, academic_year_id)

        except Exception as e:
            return Response(
                {'error': f'Lỗi khi xuất PDF toàn trường: {str(e)}'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    @action(detail=False, methods=['get'])
    def export_class_pdf(self, request):
        """Export schedules to Class PDF format"""
        try:
            # Get query parameters
            date_str = request.GET.get('date')
            semester_id = request.GET.get('semester')
            academic_year_id = request.GET.get('academicYear')
            class_id = request.GET.get('class')

            # Parse date to get week range
            if date_str:
                start_date = datetime.strptime(date_str, '%Y-%m-%d').date()
            else:
                start_date = datetime.now().date()

            # Calculate week range (Monday to Sunday)
            days_since_monday = start_date.weekday()
            week_start = start_date - timedelta(days=days_since_monday)
            week_end = week_start + timedelta(days=6)

            # Build queryset with filters
            queryset = Schedule.objects.select_related(
                'semester', 'class_obj', 'subject', 'lesson',
                'instructor', 'room', 'time_slot', 'practice_group'
            ).filter(
                status='SCHEDULED',
                schedule_date__range=[week_start, week_end]
            )

            # Apply filters
            if semester_id:
                queryset = queryset.filter(semester_id=semester_id)
            if academic_year_id:
                queryset = queryset.filter(semester__academic_year_id=academic_year_id)
            if class_id:
                queryset = queryset.filter(class_obj_id=class_id)

            return self._generate_class_pdf(queryset, week_start, week_end, semester_id, academic_year_id, class_id)

        except Exception as e:
            return Response(
                {'error': f'Lỗi khi xuất PDF theo lớp: {str(e)}'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    @action(detail=False, methods=['get'])
    def export_room_statistics_pdf(self, request):
        """Export room usage statistics to PDF format"""
        try:
            # Get query parameters
            date_str = request.GET.get('date')
            semester_id = request.GET.get('semester')
            academic_year_id = request.GET.get('academicYear')

            # Parse date to get week range
            if date_str:
                start_date = datetime.strptime(date_str, '%Y-%m-%d').date()
            else:
                start_date = datetime.now().date()

            # Calculate week range (Monday to Sunday)
            days_since_monday = start_date.weekday()
            week_start = start_date - timedelta(days=days_since_monday)
            week_end = week_start + timedelta(days=6)

            # Build queryset with filters
            queryset = Schedule.objects.select_related(
                'semester', 'class_obj', 'subject', 'lesson',
                'instructor', 'room', 'time_slot', 'practice_group'
            ).filter(
                status='SCHEDULED',
                schedule_date__range=[week_start, week_end]
            )

            # Apply filters
            if semester_id:
                queryset = queryset.filter(semester_id=semester_id)
            if academic_year_id:
                queryset = queryset.filter(semester__academic_year_id=academic_year_id)

            return self._generate_room_statistics_pdf(queryset, week_start, week_end, semester_id, academic_year_id)

        except Exception as e:
            return Response(
                {'error': f'Lỗi khi xuất PDF thống kê phòng: {str(e)}'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    @action(detail=False, methods=['get'])
    def export_pdf(self, request):
        """Export schedules to PDF format"""
        try:
            # Get query parameters (same as Excel export)
            date_str = request.GET.get('date')
            semester_id = request.GET.get('semester')
            academic_year_id = request.GET.get('academicYear')
            instructor_id = request.GET.get('instructor')
            class_id = request.GET.get('class')
            room_id = request.GET.get('room')

            # Build queryset with filters (same logic as Excel)
            queryset = Schedule.objects.select_related(
                'semester', 'class_obj', 'subject', 'lesson',
                'instructor', 'room', 'time_slot', 'practice_group'
            ).filter(status='SCHEDULED')

            # Apply filters
            if semester_id:
                queryset = queryset.filter(semester_id=semester_id)
            if academic_year_id:
                queryset = queryset.filter(semester__academic_year_id=academic_year_id)
            if instructor_id:
                queryset = queryset.filter(instructor_id=instructor_id)
            if class_id:
                queryset = queryset.filter(class_obj_id=class_id)
            if room_id:
                queryset = queryset.filter(room_id=room_id)

            # Date range filter (weekly)
            if date_str:
                try:
                    start_date = datetime.strptime(date_str, '%Y-%m-%d').date()
                    end_date = start_date + timedelta(days=6)
                    queryset = queryset.filter(
                        schedule_date__gte=start_date,
                        schedule_date__lte=end_date
                    )
                except ValueError:
                    pass

            # Order by date and time
            queryset = queryset.order_by('schedule_date', 'time_slot__start_time')

            # Get dynamic data for title (same logic as Excel)
            semester_name = "HK I"
            week_number = "1"
            instructor_info = ""

            if semester_id:
                try:
                    from core.models import Semester
                    semester = Semester.objects.select_related('academic_year').get(id=semester_id)
                    semester_name = semester.name.upper()
                    if semester_name == "HỌC KỲ 1":
                        semester_name = "HK I"
                    elif semester_name == "HỌC KỲ 2":
                        semester_name = "HK II"
                    elif "HÈ" in semester_name.upper():
                        semester_name = "HK HÈ"
                except:
                    pass

            # Calculate week number
            if date_str:
                try:
                    start_date = datetime.strptime(date_str, '%Y-%m-%d').date()
                    if semester_id:
                        try:
                            semester = Semester.objects.get(id=semester_id)
                            semester_start = semester.start_date
                            days_diff = (start_date - semester_start).days
                            week_number = str(max(1, (days_diff // 7) + 1))
                        except:
                            year_start = start_date.replace(month=1, day=1)
                            days_diff = (start_date - year_start).days
                            week_number = str(max(1, (days_diff // 7) + 1))
                    else:
                        year_start = start_date.replace(month=1, day=1)
                        days_diff = (start_date - year_start).days
                        week_number = str(max(1, (days_diff // 7) + 1))
                except:
                    week_number = "1"

            # Get instructor info
            if instructor_id:
                try:
                    from core.models import Instructor
                    instructor = Instructor.objects.get(id=instructor_id)
                    instructor_info = f"Gv: {instructor.full_name}"
                except:
                    pass
            elif queryset.exists():
                first_schedule = queryset.first()
                if first_schedule and first_schedule.instructor:
                    instructor_info = f"Gv: {first_schedule.instructor.full_name}"

            # Register Vietnamese fonts
            font_name, font_bold = register_vietnamese_fonts()

            # Create PDF
            buffer = io.BytesIO()
            doc = SimpleDocTemplate(buffer, pagesize=landscape(A4),
                                  rightMargin=20*mm, leftMargin=20*mm,
                                  topMargin=20*mm, bottomMargin=20*mm)

            # Build PDF content
            story = []
            styles = getSampleStyleSheet()

            # Header styles with Vietnamese font support
            header_style = ParagraphStyle(
                'CustomHeader',
                parent=styles['Heading1'],
                fontSize=12,
                spaceAfter=6,
                alignment=TA_CENTER,
                fontName=font_bold
            )

            title_style = ParagraphStyle(
                'CustomTitle',
                parent=styles['Heading1'],
                fontSize=14,
                spaceAfter=12,
                alignment=TA_CENTER,
                fontName=font_bold
            )

            # Header content using table for proper alignment
            header_data = [
                ['UBND TỈNH CÀ MAU', 'CỘNG HÒA XÃ HỘI CHỦ NGHĨA VIỆT NAM'],
                ['TRƯỜNG CAO ĐẲNG Y TẾ', 'Độc lập - Tự do - Hạnh phúc'],
                ['', f'Cà Mau, ngày {datetime.now().day} tháng {datetime.now().month} năm {datetime.now().year}']
            ]

            header_table = Table(header_data, colWidths=[120*mm, 120*mm])
            header_table.setStyle(TableStyle([
                # Left column - left aligned
                ('ALIGN', (0, 0), (0, -1), 'LEFT'),
                ('FONTNAME', (0, 0), (0, -1), font_bold),
                ('FONTSIZE', (0, 0), (0, -1), 12),
                ('VALIGN', (0, 0), (0, -1), 'MIDDLE'),

                # Right column - center aligned
                ('ALIGN', (1, 0), (1, -1), 'CENTER'),
                ('FONTNAME', (1, 0), (1, -1), font_bold),
                ('FONTSIZE', (1, 0), (1, -1), 12),
                ('VALIGN', (1, 0), (1, -1), 'MIDDLE'),

                # No borders
                ('GRID', (0, 0), (-1, -1), 0, colors.white),
            ]))

            story.append(header_table)

            story.append(Spacer(1, 12))

            # Title
            story.append(Paragraph(f"LỊCH GIẢNG TUẦN {week_number}- {semester_name}", title_style))

            # Date range
            if date_str:
                start_date = datetime.strptime(date_str, '%Y-%m-%d').date()
                end_date = start_date + timedelta(days=6)
                story.append(Paragraph(f"Từ ngày {start_date.strftime('%d/%m/%Y')} đến ngày {end_date.strftime('%d/%m/%Y')}", header_style))

            story.append(Spacer(1, 12))

            # Instructor info
            if instructor_info:
                story.append(Paragraph(instructor_info, header_style))
                story.append(Spacer(1, 12))

            # Create table data
            table_data = []

            # Table headers
            headers = ['Thứ-Ngày', 'Buổi', 'Thời gian', 'NỘI DUNG GIẢNG DẠY']
            table_data.append(headers)

            # Calculate week start date
            if date_str:
                week_start = datetime.strptime(date_str, '%Y-%m-%d').date()
                week_start = week_start - timedelta(days=week_start.weekday())
            else:
                today = datetime.now().date()
                week_start = today - timedelta(days=today.weekday())

            # Create schedule lookup
            schedule_lookup = {}
            for schedule in queryset:
                date_key = schedule.schedule_date
                session_key = 'S' if schedule.time_slot.start_time.hour < 12 else ('C' if schedule.time_slot.start_time.hour < 18 else 'T')
                key = (date_key, session_key)
                if key not in schedule_lookup:
                    schedule_lookup[key] = []
                schedule_lookup[key].append(schedule)

            # Generate table rows
            weekday_names = ['THỨ 2', 'THỨ 3', 'THỨ 4', 'THỨ 5', 'THỨ 6', 'THỨ 7', 'CHỦ NHẬT']
            sessions = [('S', '7h30'), ('C', '13h30'), ('T', '18h30')]

            for day_idx in range(7):
                current_date = week_start + timedelta(days=day_idx)
                weekday = weekday_names[day_idx]
                date_str_display = current_date.strftime('%d/%m/%Y')

                for session_idx, (session_code, default_time) in enumerate(sessions):
                    row = []

                    # Date column
                    if session_idx == 0:
                        row.append(f"{weekday} -\n{date_str_display}")
                    else:
                        row.append("")

                    # Session
                    row.append(session_code)

                    # Time
                    row.append(default_time)

                    # Content with proper encoding
                    key = (current_date, session_code)
                    if key in schedule_lookup:
                        content_lines = []
                        for schedule in schedule_lookup[key]:
                            # Ensure proper UTF-8 encoding for Vietnamese text
                            subject_name = str(schedule.subject.name).upper()
                            content_lines.append(f"LỚP CĐ {subject_name}")
                            content_lines.append(f"MÔN: {subject_name}")
                            if schedule.lesson:
                                lesson_title = str(schedule.lesson.title)
                                content_lines.append(f"Bài: {lesson_title}")
                            if schedule.instructor:
                                instructor_name = str(schedule.instructor.full_name)
                                content_lines.append(f"GV: {instructor_name}")
                            content_lines.append("")
                        content_text = "\n".join(content_lines).strip()
                        row.append(content_text)
                    else:
                        row.append("")

                    table_data.append(row)

            # Create table
            table = Table(table_data, colWidths=[60*mm, 20*mm, 30*mm, 120*mm])

            # Table style with Vietnamese font support
            table.setStyle(TableStyle([
                # Header row
                ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
                ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
                ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                ('FONTNAME', (0, 0), (-1, 0), font_bold),
                ('FONTSIZE', (0, 0), (-1, 0), 10),
                ('BOTTOMPADDING', (0, 0), (-1, 0), 12),

                # Data rows
                ('FONTNAME', (0, 1), (-1, -1), font_name),
                ('FONTSIZE', (0, 1), (-1, -1), 8),
                ('GRID', (0, 0), (-1, -1), 1, colors.black),
                ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
                ('ROWBACKGROUNDS', (0, 1), (-1, -1), [colors.white, colors.lightgrey]),
            ]))

            story.append(table)
            story.append(Spacer(1, 20))

            # Footer with Vietnamese font support
            footer_style = ParagraphStyle(
                'Footer',
                parent=styles['Normal'],
                fontSize=9,
                alignment=TA_LEFT,
                fontName=font_name
            )

            story.append(Paragraph("Ghi chú:", footer_style))
            story.append(Paragraph("- Thầy cô nào có lịch dạy và Phòng Đào tạo (02903.828304) hoặc gọi CN. Thơi (0944.269.629).", footer_style))
            story.append(Spacer(1, 20))

            # Signatures
            signature_style = ParagraphStyle(
                'Signature',
                parent=styles['Normal'],
                fontSize=10,
                alignment=TA_CENTER,
                fontName='Helvetica-Bold'
            )

            signature_table_data = [
                ['HIỆU TRƯỞNG', '', '', 'TRƯỞNG PHÒNG'],
                ['', '', '', ''],
                ['', '', '', ''],
                ['TS. Huỳnh Ngọc Linh', '', '', 'ThS. Vũ Văn Hương']
            ]

            signature_table = Table(signature_table_data, colWidths=[60*mm, 40*mm, 40*mm, 60*mm])
            signature_table.setStyle(TableStyle([
                ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                ('FONTNAME', (0, 0), (-1, -1), font_bold),
                ('FONTSIZE', (0, 0), (-1, -1), 10),
                ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
            ]))

            story.append(signature_table)

            # Build PDF
            doc.build(story)

            # Create response
            buffer.seek(0)
            response = HttpResponse(buffer.getvalue(), content_type='application/pdf')

            # Generate filename
            filename = f"LichGiangDay_{datetime.now().strftime('%Y%m%d_%H%M%S')}.pdf"
            response['Content-Disposition'] = f'attachment; filename="{filename}"'

            return response

        except Exception as e:
            return Response(
                {'error': f'Lỗi khi xuất PDF: {str(e)}'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    def _generate_campus_wide_pdf(self, queryset, week_start, week_end, semester_id, academic_year_id):
        """Generate campus-wide PDF"""
        # Get semester info for header
        semester_info = None
        if semester_id:
            semester_info = Semester.objects.select_related('academic_year').filter(id=semester_id).first()
        elif academic_year_id:
            semester_info = Semester.objects.select_related('academic_year').filter(
                academic_year_id=academic_year_id, is_current=True
            ).first()
        else:
            semester_info = Semester.objects.select_related('academic_year').filter(is_current=True).first()

        # Calculate week number
        week_number = week_start.isocalendar()[1]

        # Register Vietnamese fonts
        font_name, font_bold = register_vietnamese_fonts()

        # Create PDF
        buffer = io.BytesIO()
        doc = SimpleDocTemplate(buffer, pagesize=landscape(A4),
                              rightMargin=15*mm, leftMargin=15*mm,
                              topMargin=15*mm, bottomMargin=15*mm)

        # Build PDF content
        story = []
        styles = getSampleStyleSheet()

        # Header styles
        header_style = ParagraphStyle(
            'CustomHeader',
            parent=styles['Heading1'],
            fontSize=12,
            spaceAfter=6,
            alignment=TA_CENTER,
            fontName=font_bold
        )

        title_style = ParagraphStyle(
            'CustomTitle',
            parent=styles['Heading1'],
            fontSize=14,
            spaceAfter=12,
            alignment=TA_CENTER,
            fontName=font_bold
        )

        # Header content - match campus wide Excel template
        story.append(Paragraph("UBND TỈNH CÀ MAU", header_style))
        story.append(Paragraph("TRƯỜNG CAO ĐẲNG Y TẾ", header_style))
        story.append(Spacer(1, 12))
        story.append(Paragraph("LỊCH GIẢNG DẠY TOÀN TRƯỜNG", title_style))
        story.append(Spacer(1, 12))

        date_range_text = f"TỪ NGÀY {week_start.strftime('%d/%m/%Y')} ĐẾN NGÀY {week_end.strftime('%d/%m/%Y')}"
        story.append(Paragraph(date_range_text, header_style))

        semester_text = f"TUẦN {week_number}"
        if semester_info:
            semester_text += f" - {semester_info.name.upper()}"
        story.append(Paragraph(semester_text, header_style))
        story.append(Spacer(1, 20))

        # Get all classes and time slots
        classes = Class.objects.filter(
            id__in=queryset.values_list('class_obj_id', flat=True)
        ).order_by('code')

        time_slots = TimeSlot.objects.filter(
            id__in=queryset.values_list('time_slot_id', flat=True)
        ).order_by('session', 'start_time')

        # Create schedule lookup
        schedule_lookup = {}
        for schedule in queryset:
            key = (schedule.schedule_date, schedule.time_slot_id, schedule.class_obj_id)
            if key not in schedule_lookup:
                schedule_lookup[key] = []
            schedule_lookup[key].append(schedule)

        # Build table data
        table_data = []

        # Header row
        header_row = ['THỨ', 'BUỔI']
        for class_obj in classes:
            header_row.append(class_obj.code)
        table_data.append(header_row)

        # Days of week
        days_vn = ['THỨ 2', 'THỨ 3', 'THỨ 4', 'THỨ 5', 'THỨ 6', 'THỨ 7', 'CN']

        # Generate data for each day
        for day_idx in range(7):
            current_date = week_start + timedelta(days=day_idx)
            day_name = days_vn[day_idx]

            # Get time slots for this day
            day_time_slots = time_slots.filter(
                id__in=queryset.filter(schedule_date=current_date).values_list('time_slot_id', flat=True)
            )

            if not day_time_slots.exists():
                # Add empty row if no schedules
                row = [f'{day_name}\n{current_date.strftime("%d/%m")}', '']
                for _ in classes:
                    row.append('')
                table_data.append(row)
                continue

            first_slot = True
            for time_slot in day_time_slots:
                row = []

                # Day column (only for first slot)
                if first_slot:
                    row.append(f'{day_name}\n{current_date.strftime("%d/%m")}')
                    first_slot = False
                else:
                    row.append('')

                # Session column
                session_map = {'SANG': 'S', 'CHIEU': 'C', 'TOI': 'T'}
                session_code = session_map.get(time_slot.session, time_slot.session)
                row.append(f'{session_code}\n{time_slot.start_time.strftime("%H:%M")}')

                # Class columns
                for class_obj in classes:
                    key = (current_date, time_slot.id, class_obj.id)
                    if key in schedule_lookup:
                        content_lines = []
                        for schedule in schedule_lookup[key]:
                            content_lines.append(f"{schedule.subject.code}")
                            if schedule.room:
                                content_lines.append(f"P.{schedule.room.code}")
                        content = '\n'.join(content_lines)
                        row.append(content)
                    else:
                        row.append('')

                table_data.append(row)

        # Create table
        col_widths = [25*mm, 20*mm] + [25*mm] * len(classes)
        table = Table(table_data, colWidths=col_widths)
        table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (-1, 0), colors.lightgrey),
            ('TEXTCOLOR', (0, 0), (-1, 0), colors.black),
            ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
            ('FONTNAME', (0, 0), (-1, 0), font_bold),
            ('FONTSIZE', (0, 0), (-1, 0), 9),
            ('FONTNAME', (0, 1), (-1, -1), font_name),
            ('FONTSIZE', (0, 1), (-1, -1), 8),
            ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
            ('GRID', (0, 0), (-1, -1), 1, colors.black),
            ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
        ]))

        story.append(table)
        story.append(Spacer(1, 20))

        # Signatures - 3 equal columns, no borders like instructor template
        signature_data = [
            ['GIÁO VỤ', 'KT. TRƯỞNG PHÒNG\nP. TRƯỞNG PHÒNG', 'HIỆU TRƯỞNG'],
            ['', '', ''],
            ['', '', ''],
            ['', '', ''],
            ['Cn. LÊ MINH THỜI', 'Ðs.CKI. HUỲNH THANH BÌNH', 'TS. HUỲNH NGỌC LINH']
        ]

        signature_table = Table(signature_data, colWidths=[80*mm, 80*mm, 80*mm])
        signature_table.setStyle(TableStyle([
            ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
            ('FONTNAME', (0, 0), (-1, -1), font_bold),
            ('FONTSIZE', (0, 0), (-1, 0), 10),
            ('FONTSIZE', (0, 4), (-1, 4), 10),
            ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
            # No borders - clean layout like instructor template
        ]))

        story.append(signature_table)

        # Build PDF
        doc.build(story)

        # Create response
        buffer.seek(0)
        response = HttpResponse(buffer.getvalue(), content_type='application/pdf')

        # Generate filename
        filename = f"LichToanTruong_{datetime.now().strftime('%Y%m%d_%H%M%S')}.pdf"
        response['Content-Disposition'] = f'attachment; filename="{filename}"'

        return response

    def _generate_class_pdf(self, queryset, week_start, week_end, semester_id, academic_year_id, class_id):
        """Generate class-specific PDF"""
        # Get class info
        class_info = None
        if class_id:
            class_info = Class.objects.filter(id=class_id).first()

        # Get semester info for header
        semester_info = None
        if semester_id:
            semester_info = Semester.objects.select_related('academic_year').filter(id=semester_id).first()
        elif academic_year_id:
            semester_info = Semester.objects.select_related('academic_year').filter(
                academic_year_id=academic_year_id, is_current=True
            ).first()
        else:
            semester_info = Semester.objects.select_related('academic_year').filter(is_current=True).first()

        # Calculate week number
        week_number = week_start.isocalendar()[1]

        # Register Vietnamese fonts
        font_name, font_bold = register_vietnamese_fonts()

        # Create PDF
        buffer = io.BytesIO()
        doc = SimpleDocTemplate(buffer, pagesize=A4,
                              rightMargin=20*mm, leftMargin=20*mm,
                              topMargin=20*mm, bottomMargin=20*mm)

        # Build PDF content
        story = []
        styles = getSampleStyleSheet()

        # Header styles
        header_style = ParagraphStyle(
            'CustomHeader',
            parent=styles['Heading1'],
            fontSize=12,
            spaceAfter=6,
            alignment=TA_CENTER,
            fontName=font_bold
        )

        title_style = ParagraphStyle(
            'CustomTitle',
            parent=styles['Heading1'],
            fontSize=14,
            spaceAfter=12,
            alignment=TA_CENTER,
            fontName=font_bold
        )

        # Header content - match class Excel template exactly
        story.append(Paragraph("TRƯỜNG CAO ĐẲNG Y TẾ CÀ MAU", header_style))
        story.append(Paragraph("PHÒNG ĐÀO TẠO", header_style))
        story.append(Spacer(1, 12))
        story.append(Paragraph("KẾ HOẠCH PHÂN BỔ GIẢNG DẠY HÀNG TUẦN", title_style))

        date_range_text = f"TỪ NGÀY {week_start.strftime('%d/%m/%Y')} ĐẾN NGÀY {week_end.strftime('%d/%m/%Y')}"
        story.append(Paragraph(date_range_text, header_style))

        semester_text = f"TUẦN {week_number}"
        if semester_info:
            semester_text += f" - {semester_info.name.upper()}"
        story.append(Paragraph(semester_text, header_style))

        # Class info in red like Excel
        class_text = "LỚP CD."
        if class_info:
            class_text += f" {class_info.name.upper()}"

        class_style = ParagraphStyle(
            'ClassStyle',
            parent=styles['Heading1'],
            fontSize=16,
            spaceAfter=12,
            alignment=TA_CENTER,
            fontName=font_bold,
            textColor=colors.red
        )
        story.append(Paragraph(class_text, class_style))
        story.append(Spacer(1, 20))

        # Build table data
        table_data = []

        # Header row - match Excel exactly (only 3 columns)
        table_data.append(['THỨ', 'BUỔI', 'NỘI DUNG'])

        # Days of week
        days_vn = ['THỨ 2', 'THỨ 3', 'THỨ 4', 'THỨ 5', 'THỨ 6', 'THỨ 7', 'CHỦ NHẬT']
        sessions = [
            ('S', 'SANG', '7h30', colors.yellow),
            ('C', 'CHIEU', '13h30', colors.lightblue),
            ('T', 'TOI', '18h30', colors.plum)
        ]

        # Create schedule lookup
        schedule_lookup = {}
        for schedule in queryset:
            session_map = {'SANG': 'S', 'CHIEU': 'C', 'TOI': 'T'}
            session_code = session_map.get(schedule.time_slot.session, schedule.time_slot.session)
            key = (schedule.schedule_date, session_code)
            if key not in schedule_lookup:
                schedule_lookup[key] = []
            schedule_lookup[key].append(schedule)

        # Generate data for each day
        for day_idx in range(7):
            current_date = week_start + timedelta(days=day_idx)
            day_name = days_vn[day_idx]

            for session_idx, (session_code, session_name, default_time, session_color) in enumerate(sessions):
                row = []

                # Day column (only for first session)
                if session_idx == 0:
                    row.append(f'{day_name}\n{current_date.strftime("%d/%m/%Y")}')
                else:
                    row.append('')

                # Session column with time
                row.append(f'{session_code}\n({default_time})')

                # Content column
                key = (current_date, session_code)
                if key in schedule_lookup:
                    content_lines = []
                    for schedule in schedule_lookup[key]:
                        content_lines.append(f"MÔN: {schedule.subject.name}")
                        if schedule.lesson:
                            content_lines.append(f"Bài: {schedule.lesson.title}")
                        if schedule.instructor:
                            content_lines.append(f"GV: {schedule.instructor.full_name}")
                        if schedule.room:
                            content_lines.append(f"Phòng: {schedule.room.code}")
                        content_lines.append("---")
                    content = '\n'.join(content_lines).strip()
                    if content.endswith("---"):
                        content = content[:-3].strip()
                    row.append(content)
                else:
                    row.append('Nghỉ')

                table_data.append(row)

        # Create table - match Excel layout (3 columns)
        col_widths = [30*mm, 25*mm, 115*mm]
        table = Table(table_data, colWidths=col_widths)

        # Build table style with session colors
        table_style = [
            ('BACKGROUND', (0, 0), (-1, 0), colors.lightgrey),
            ('TEXTCOLOR', (0, 0), (-1, 0), colors.black),
            ('ALIGN', (0, 0), (1, -1), 'CENTER'),
            ('ALIGN', (2, 0), (2, -1), 'LEFT'),
            ('FONTNAME', (0, 0), (-1, 0), font_bold),
            ('FONTSIZE', (0, 0), (-1, 0), 10),
            ('FONTNAME', (0, 1), (-1, -1), font_name),
            ('FONTSIZE', (0, 1), (-1, -1), 9),
            ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
            ('GRID', (0, 0), (-1, -1), 1, colors.black),
            ('VALIGN', (0, 0), (-1, -1), 'TOP'),
            ('LEFTPADDING', (2, 1), (2, -1), 6),
            ('RIGHTPADDING', (2, 1), (2, -1), 6),
        ]

        # Add session colors to rows
        session_colors = [colors.yellow, colors.lightblue, colors.plum]
        for row_idx in range(1, len(table_data)):
            session_idx = (row_idx - 1) % 3
            color = session_colors[session_idx]
            table_style.append(('BACKGROUND', (1, row_idx), (2, row_idx), color))

        table.setStyle(TableStyle(table_style))

        story.append(table)
        story.append(Spacer(1, 20))

        # Signatures
        signature_data = [
            ['', '', 'TRƯỞNG PHÒNG'],
            ['', '', ''],
            ['', '', ''],
            ['', '', 'ThS. Vũ Văn Hương']
        ]

        signature_table = Table(signature_data, colWidths=[60*mm, 50*mm, 60*mm])
        signature_table.setStyle(TableStyle([
            ('ALIGN', (2, 0), (2, -1), 'CENTER'),
            ('FONTNAME', (2, 0), (2, -1), font_bold),
            ('FONTSIZE', (2, 0), (2, -1), 10),
            ('VALIGN', (2, 0), (2, -1), 'MIDDLE'),
        ]))

        story.append(signature_table)

        # Build PDF
        doc.build(story)

        # Create response
        buffer.seek(0)
        response = HttpResponse(buffer.getvalue(), content_type='application/pdf')

        # Generate filename
        filename = f"LichTheoLop_{datetime.now().strftime('%Y%m%d_%H%M%S')}.pdf"
        response['Content-Disposition'] = f'attachment; filename="{filename}"'

        return response

    def _generate_room_statistics_pdf(self, queryset, week_start, week_end, semester_id, academic_year_id):
        """Generate room statistics PDF"""
        # Get all rooms used in the schedules
        rooms = Room.objects.filter(
            id__in=queryset.values_list('room_id', flat=True)
        ).order_by('code')

        # Get semester info for header
        semester_info = None
        if semester_id:
            semester_info = Semester.objects.select_related('academic_year').filter(id=semester_id).first()
        elif academic_year_id:
            semester_info = Semester.objects.select_related('academic_year').filter(
                academic_year_id=academic_year_id, is_current=True
            ).first()
        else:
            semester_info = Semester.objects.select_related('academic_year').filter(is_current=True).first()

        # Calculate week number
        week_number = week_start.isocalendar()[1]

        # Register Vietnamese fonts
        font_name, font_bold = register_vietnamese_fonts()

        # Create PDF
        buffer = io.BytesIO()
        doc = SimpleDocTemplate(buffer, pagesize=landscape(A4),
                              rightMargin=15*mm, leftMargin=15*mm,
                              topMargin=15*mm, bottomMargin=15*mm)

        # Build PDF content
        story = []
        styles = getSampleStyleSheet()

        # Header styles
        header_style = ParagraphStyle(
            'CustomHeader',
            parent=styles['Heading1'],
            fontSize=12,
            spaceAfter=6,
            alignment=TA_CENTER,
            fontName=font_bold
        )

        title_style = ParagraphStyle(
            'CustomTitle',
            parent=styles['Heading1'],
            fontSize=14,
            spaceAfter=12,
            alignment=TA_CENTER,
            fontName=font_bold
        )

        # Header content
        story.append(Paragraph("TRƯỜNG CAO ĐẲNG Y TẾ CÀ MAU", header_style))
        story.append(Paragraph("PHÒNG ĐÀO TẠO", header_style))
        story.append(Spacer(1, 12))
        story.append(Paragraph("BẢNG THEO DÕI PHÒNG HỌC", title_style))

        date_range_text = f"TỪ NGÀY {week_start.strftime('%d/%m/%Y')} ĐẾN NGÀY {week_end.strftime('%d/%m/%Y')}"
        story.append(Paragraph(date_range_text, header_style))

        semester_text = f"TUẦN {week_number}"
        if semester_info:
            semester_text += f" - {semester_info.name.upper()}"
        story.append(Paragraph(semester_text, header_style))
        story.append(Spacer(1, 20))

        # Build table data
        table_data = []

        # Header row
        header_row = ['THỨ', 'BUỔI']
        for room in rooms:
            header_row.append(room.code)
        table_data.append(header_row)

        # Days of week
        days_vn = ['THỨ 2', 'THỨ 3', 'THỨ 4', 'THỨ 5', 'THỨ 6', 'THỨ 7', 'CN']
        sessions = [
            ('S', 'SANG'),
            ('C', 'CHIEU'),
            ('T', 'TOI')
        ]

        # Generate data for each day
        for day_idx in range(7):
            current_date = week_start + timedelta(days=day_idx)
            day_name = days_vn[day_idx]

            for session_idx, (session_code, session_name) in enumerate(sessions):
                row = []

                # Day column (only for first session)
                if session_idx == 0:
                    row.append(f'{day_name}\n{current_date.strftime("%d/%m")}')
                else:
                    row.append('')

                # Session column
                row.append(session_code)

                # Room usage data
                for room in rooms:
                    # Count schedules for this room, date, and session
                    count = queryset.filter(
                        schedule_date=current_date,
                        room=room,
                        time_slot__session=session_name
                    ).count()

                    if count > 0:
                        row.append(str(count))
                    else:
                        row.append('')

                table_data.append(row)

        # Create table
        col_widths = [25*mm, 15*mm] + [15*mm] * len(rooms)
        table = Table(table_data, colWidths=col_widths)

        # Define colors for different usage levels
        table_style = [
            ('BACKGROUND', (0, 0), (-1, 0), colors.lightgrey),
            ('TEXTCOLOR', (0, 0), (-1, 0), colors.black),
            ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
            ('FONTNAME', (0, 0), (-1, 0), font_bold),
            ('FONTSIZE', (0, 0), (-1, 0), 9),
            ('FONTNAME', (0, 1), (-1, -1), font_name),
            ('FONTSIZE', (0, 1), (-1, -1), 8),
            ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
            ('GRID', (0, 0), (-1, -1), 1, colors.black),
            ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
        ]

        # Add background colors based on usage
        for row_idx in range(1, len(table_data)):
            for col_idx in range(2, len(table_data[row_idx])):
                cell_value = table_data[row_idx][col_idx]
                if cell_value and cell_value.isdigit():
                    count = int(cell_value)
                    if count >= 3:
                        table_style.append(('BACKGROUND', (col_idx, row_idx), (col_idx, row_idx), colors.lightblue))
                    elif count >= 2:
                        table_style.append(('BACKGROUND', (col_idx, row_idx), (col_idx, row_idx), colors.lightcyan))
                    elif count >= 1:
                        table_style.append(('BACKGROUND', (col_idx, row_idx), (col_idx, row_idx), colors.lightyellow))

        table.setStyle(TableStyle(table_style))

        story.append(table)
        story.append(Spacer(1, 20))

        # Signatures
        signature_data = [
            ['', '', 'TRƯỞNG PHÒNG', ''],
            ['', '', '', ''],
            ['', '', '', ''],
            ['', '', 'ThS. Vũ Văn Hương', '']
        ]

        signature_table = Table(signature_data, colWidths=[60*mm, 40*mm, 40*mm, 60*mm])
        signature_table.setStyle(TableStyle([
            ('ALIGN', (2, 0), (2, -1), 'CENTER'),
            ('FONTNAME', (2, 0), (2, -1), font_bold),
            ('FONTSIZE', (2, 0), (2, -1), 10),
            ('VALIGN', (2, 0), (2, -1), 'MIDDLE'),
        ]))

        story.append(signature_table)

        # Build PDF
        doc.build(story)

        # Create response
        buffer.seek(0)
        response = HttpResponse(buffer.getvalue(), content_type='application/pdf')

        # Generate filename
        filename = f"ThongKePhong_{datetime.now().strftime('%Y%m%d_%H%M%S')}.pdf"
        response['Content-Disposition'] = f'attachment; filename="{filename}"'

        return response


class ScheduleConflictViewSet(viewsets.ModelViewSet):
    queryset = ScheduleConflict.objects.select_related(
        'schedule', 'conflict_with_schedule'
    ).all()
    serializer_class = ScheduleConflictSerializer
    permission_classes = [IsAuthenticated]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter]
    filterset_fields = ['conflict_type', 'is_resolved']

    @action(detail=True, methods=['post'])
    def resolve(self, request, pk=None):
        """Mark conflict as resolved"""
        conflict = self.get_object()
        conflict.is_resolved = True
        conflict.save()
        return Response({'status': 'conflict resolved'})
