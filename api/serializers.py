from rest_framework import serializers
from core.models import (
    Permission, Role, User, Campus, Department, Major, AcademicYear, Semester,
    Class, Subject, Lesson, Instructor, Room, TimeSlot, PracticeGroup,
    Schedule, ScheduleConflict
)


class PermissionSerializer(serializers.ModelSerializer):
    class Meta:
        model = Permission
        fields = ['id', 'name', 'codename', 'description', 'module', 'created_at']


class RoleSerializer(serializers.ModelSerializer):
    permission_count = serializers.SerializerMethodField()

    class Meta:
        model = Role
        fields = ['id', 'name', 'description', 'permissions', 'permission_count', 'created_at']

    def get_permission_count(self, obj):
        return obj.permissions.count()


class RoleDetailSerializer(serializers.ModelSerializer):
    permissions = PermissionSerializer(many=True, read_only=True)
    permission_ids = serializers.ListField(
        child=serializers.IntegerField(),
        write_only=True,
        required=False
    )

    class Meta:
        model = Role
        fields = ['id', 'name', 'description', 'permissions', 'permission_ids', 'created_at']

    def update(self, instance, validated_data):
        permission_ids = validated_data.pop('permission_ids', None)

        # Update basic fields
        for attr, value in validated_data.items():
            setattr(instance, attr, value)
        instance.save()

        # Update permissions if provided
        if permission_ids is not None:
            instance.permissions.set(permission_ids)

        return instance


class UserSerializer(serializers.ModelSerializer):
    role_name = serializers.CharField(source='role.name', read_only=True)
    password = serializers.CharField(write_only=True, required=False)

    class Meta:
        model = User
        fields = ['id', 'username', 'email', 'full_name', 'role', 'role_name', 'is_active', 'is_staff', 'date_joined', 'password']
        extra_kwargs = {
            'password': {'write_only': True}
        }

    def create(self, validated_data):
        password = validated_data.pop('password', None)
        user = User.objects.create(**validated_data)
        if password:
            user.set_password(password)
            user.save()
        return user

    def update(self, instance, validated_data):
        # Remove password from validated_data if present (use change_password endpoint instead)
        validated_data.pop('password', None)
        return super().update(instance, validated_data)


class CampusSerializer(serializers.ModelSerializer):
    class Meta:
        model = Campus
        fields = '__all__'


class DepartmentSerializer(serializers.ModelSerializer):
    campus_name = serializers.CharField(source='campus.name', read_only=True)
    
    class Meta:
        model = Department
        fields = ['id', 'campus', 'campus_name', 'code', 'name', 'is_active']


class MajorSerializer(serializers.ModelSerializer):
    department_name = serializers.CharField(source='department.name', read_only=True)
    
    class Meta:
        model = Major
        fields = ['id', 'department', 'department_name', 'code', 'name', 'duration_years', 'is_active']


class AcademicYearSerializer(serializers.ModelSerializer):
    class Meta:
        model = AcademicYear
        fields = '__all__'


class SemesterSerializer(serializers.ModelSerializer):
    academic_year_code = serializers.CharField(source='academic_year.year_code', read_only=True)
    
    class Meta:
        model = Semester
        fields = ['id', 'academic_year', 'academic_year_code', 'semester_code', 'name', 'start_date', 'end_date', 'is_current']


class ClassSerializer(serializers.ModelSerializer):
    major_name = serializers.CharField(source='major.name', read_only=True)
    
    class Meta:
        model = Class
        fields = ['id', 'major', 'major_name', 'code', 'name', 'year_level', 'max_students', 'is_active']


class SubjectSerializer(serializers.ModelSerializer):
    department_name = serializers.CharField(source='department.name', read_only=True)
    total_hours = serializers.SerializerMethodField()
    
    def get_total_hours(self, obj):
        return obj.theory_hours + obj.practice_hours
    
    class Meta:
        model = Subject
        fields = ['id', 'department', 'department_name', 'code', 'name', 'credits', 'theory_hours', 'practice_hours', 'total_hours', 'is_active']


class LessonSerializer(serializers.ModelSerializer):
    subject_name = serializers.CharField(source='subject.name', read_only=True)
    subject_code = serializers.CharField(source='subject.code', read_only=True)
    
    class Meta:
        model = Lesson
        fields = ['id', 'subject', 'subject_name', 'subject_code', 'lesson_number', 'title', 'lesson_type', 'duration_hours']


class InstructorSerializer(serializers.ModelSerializer):
    department_name = serializers.CharField(source='department.name', read_only=True)
    user_username = serializers.CharField(source='user.username', read_only=True)

    class Meta:
        model = Instructor
        fields = ['id', 'user', 'user_username', 'department', 'department_name', 'employee_code', 'full_name', 'email', 'phone', 'max_teaching_hours', 'is_active']

    def validate_employee_code(self, value):
        """Custom validation for employee_code to handle unique constraint"""
        # Skip validation if this is an update and employee_code hasn't changed
        if self.instance and self.instance.employee_code == value:
            return value

        # Check if employee_code already exists
        existing_instructor = Instructor.objects.filter(employee_code=value).first()
        if existing_instructor:
            # If we're in the context of user creation (from perform_create),
            # let the view handle this logic
            request = self.context.get('request')
            if request and hasattr(request, 'user'):
                # This will be handled in perform_create
                pass
            else:
                raise serializers.ValidationError(f"Mã giảng viên {value} đã tồn tại")

        return value


class RoomSerializer(serializers.ModelSerializer):
    campus_name = serializers.CharField(source='campus.name', read_only=True)
    
    class Meta:
        model = Room
        fields = ['id', 'campus', 'campus_name', 'area_code', 'code', 'name', 'room_type', 'capacity', 'is_available']


class TimeSlotSerializer(serializers.ModelSerializer):
    class Meta:
        model = TimeSlot
        fields = '__all__'


class PracticeGroupSerializer(serializers.ModelSerializer):
    class_name = serializers.CharField(source='class_obj.name', read_only=True)
    class_code = serializers.CharField(source='class_obj.code', read_only=True)
    subject_name = serializers.CharField(source='subject.name', read_only=True)
    subject_code = serializers.CharField(source='subject.code', read_only=True)
    
    class Meta:
        model = PracticeGroup
        fields = ['id', 'class_obj', 'class_name', 'class_code', 'subject', 'subject_name', 'subject_code', 'group_number', 'max_students']


class ScheduleSerializer(serializers.ModelSerializer):
    semester_name = serializers.CharField(source='semester.name', read_only=True)
    class_name = serializers.CharField(source='class_obj.name', read_only=True)
    class_code = serializers.CharField(source='class_obj.code', read_only=True)
    subject_name = serializers.CharField(source='subject.name', read_only=True)
    subject_code = serializers.CharField(source='subject.code', read_only=True)
    lesson_title = serializers.CharField(source='lesson.title', read_only=True)
    instructor_name = serializers.CharField(source='instructor.full_name', read_only=True)
    room_name = serializers.CharField(source='room.name', read_only=True)
    room_code = serializers.CharField(source='room.code', read_only=True)
    time_slot_name = serializers.CharField(source='time_slot.slot_name', read_only=True)
    time_slot_start = serializers.TimeField(source='time_slot.start_time', read_only=True)
    time_slot_end = serializers.TimeField(source='time_slot.end_time', read_only=True)
    practice_group_number = serializers.IntegerField(source='practice_group.group_number', read_only=True)
    created_by_name = serializers.CharField(source='created_by.full_name', read_only=True)
    
    class Meta:
        model = Schedule
        fields = [
            'id', 'semester', 'semester_name', 'class_obj', 'class_name', 'class_code',
            'subject', 'subject_name', 'subject_code', 'lesson', 'lesson_title',
            'instructor', 'instructor_name', 'room', 'room_name', 'room_code',
            'time_slot', 'time_slot_name', 'time_slot_start', 'time_slot_end',
            'practice_group', 'practice_group_number', 'schedule_date', 'day_of_week',
            'lesson_type', 'duration_hours', 'coefficient', 'status', 'notes',
            'created_by', 'created_by_name', 'created_at'
        ]


class ScheduleConflictSerializer(serializers.ModelSerializer):
    schedule_info = serializers.CharField(source='schedule.__str__', read_only=True)
    conflict_with_info = serializers.CharField(source='conflict_with_schedule.__str__', read_only=True)
    
    class Meta:
        model = ScheduleConflict
        fields = ['id', 'schedule', 'schedule_info', 'conflict_type', 'conflict_with_schedule', 'conflict_with_info', 'is_resolved', 'created_at']


# Simplified serializers for dropdown/select options
class SimpleOptionSerializer(serializers.Serializer):
    id = serializers.IntegerField()
    name = serializers.CharField()
    code = serializers.CharField(required=False)


class ScheduleCreateSerializer(serializers.ModelSerializer):
    """Serializer for creating schedules with validation"""
    
    class Meta:
        model = Schedule
        fields = [
            'semester', 'class_obj', 'subject', 'lesson', 'instructor', 'room',
            'time_slot', 'practice_group', 'schedule_date', 'lesson_type',
            'duration_hours', 'coefficient', 'notes'
        ]
    
    def validate(self, data):
        """Custom validation for schedule creation"""
        # Only validate conflicts if we have all required fields
        required_fields = ['instructor', 'schedule_date', 'time_slot']

        # For partial updates, merge with existing instance data
        if self.instance and self.partial:
            # Get current values from instance for missing fields
            for field in required_fields:
                if field not in data:
                    if field == 'instructor':
                        data[field] = self.instance.instructor
                    elif field == 'schedule_date':
                        data[field] = self.instance.schedule_date
                    elif field == 'time_slot':
                        data[field] = self.instance.time_slot

        # Only check conflicts if we have all required fields
        if all(field in data for field in required_fields):
            # Check for instructor conflicts
            instructor_conflicts = Schedule.objects.filter(
                instructor=data['instructor'],
                schedule_date=data['schedule_date'],
                time_slot=data['time_slot'],
                status='SCHEDULED'
            ).exclude(id=self.instance.id if self.instance else None)

            if instructor_conflicts.exists():
                raise serializers.ValidationError(
                    f"Giảng viên {data['instructor'].full_name} đã có lịch dạy vào thời gian này"
                )

            # Check for room conflicts (if room is being updated)
            if 'room' in data:
                room_conflicts = Schedule.objects.filter(
                    room=data['room'],
                    schedule_date=data['schedule_date'],
                    time_slot=data['time_slot'],
                    status='SCHEDULED'
                ).exclude(id=self.instance.id if self.instance else None)

                if room_conflicts.exists():
                    raise serializers.ValidationError(
                        f"Phòng {data['room'].name} đã được sử dụng vào thời gian này"
                    )

            # Check for class conflicts (if class is being updated)
            if 'class_obj' in data:
                class_conflicts = Schedule.objects.filter(
                    class_obj=data['class_obj'],
                    schedule_date=data['schedule_date'],
                    time_slot=data['time_slot'],
                    status='SCHEDULED'
                ).exclude(id=self.instance.id if self.instance else None)

                if class_conflicts.exists():
                    raise serializers.ValidationError(
                        f"Lớp {data['class_obj'].name} đã có lịch học vào thời gian này"
                    )

        return data
    
    def create(self, validated_data):
        # Set created_by from request user
        validated_data['created_by'] = self.context['request'].user
        # Calculate day_of_week
        validated_data['day_of_week'] = validated_data['schedule_date'].weekday() + 1
        return super().create(validated_data)
